<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Top
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2019 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('TopHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'top.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

require_once(JPATH_SITE . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'toplog.php');
/**
 * Class TopFrontendHelper
 *
 * @since  1.6
 */
class TopHelpersTop
{
	public static $di = 1;
	public static $do = 2;
	public static $ai = 3;

	public static $page = 1;
	public static $bpage = 2;
	public static $dpage = 3;
	public static $apage = 4;
	public static $cpage = 4;
	public static $adpage = 5;
	public static $tdiopage = 6;
	public static $tdpage = 7;
	public static $all_group = 8;
	public static $iptype = 9;
	public static $cdpage = 11;
	public static $tvdpage = 12;
	public static $tvpage = 13;
	public static $ddiopage = 14;
	public static $rs485page = 15;
	public static $doorpage = 16;
	public static $doornode = 17;

	public static $types = array(

	    2=>'ip 裝置',
		1=>'網路電話',
		3=>'CCTV',
		//4=>'温濕度計',

	);


	public static $building_type = array(

		0=>'一般',
		1=>'電表',
	);

	public static $brand_temp = array(

		1=>'weema',

	);

	public static $select_rs485_ports = array(

		5809=>'5809',
		5811=>'5811',

	);

	public static $select_weema_ports = array(

		5801=>'5801',

	);

	public static $dio_weema_vendor = 1;
	public static $dio_advantech_vendor = 2;
	public static $dio_sinew_vendor = 3;
	public static $dio_soyal_vendor = 4;
	public static $dio_icp_vendor = 5;
	public static $dio_weema_485_vendor = 6;
	public static $dio_panasonic_two_line_vendor = 7;
	public static $dio_liuchuanElevator_vendor = 8;
	public static $dio_yunyangFireFighter_vendor = 9;
	public static $dio_baochungFireFighter_vendor = 10;
	public static $dio_mitsubishiElevator_vendor = 11;
	public static $dio_fujiElevator_vendor = 13;
	public static $dio_co_sensor_dio_type = 7;
	public static $dio_co_sensor_yon_gjia_dio_type = 40;
	public static $dio_pm_sensor_yon_gjia_dio_type = 41;
	public static $dio_th_co_sensor_yon_gjia_dio_type = 46;
	public static $dio_co_sensor_general_dio_type = 34;
	public static $dio_temp_humidity_dio_type = 4;
	public static $dio_temp_humidity_pinron_dio_type = 30;
	public static $dio_yon_gjia_temp_humidity_dio_type = 11;
	public static $dio_yon_gjia_temp_lux_dio_type = 42;
	public static $dio_yon_gjia_temp_humidity2_dio_type = 38;
	public static $dio_yon_gjia_temp_humidity_3_in_1_dio_type = 39;
	public static $dio_tatung_elec_meter_dio_type = 10;
	public static $dio_bender_pem_330_elec_meter_dio_type = 12;
	public static $dio_bender_pem_575_elec_meter_dio_type = 13;
	public static $dio_shihlin_elec_meter_dio_type = 14;
	public static $dio_elec_cicbaw1a2a_dio_type = 36;
	public static $dio_elec_cicbaw2c_dio_type = 47;
	public static $dio_elec_acuvim_dio_type = 43;
	public static $dio_elec_daepm210_dio_type = 44;
	public static $dio_opcda_elec_meter_dio_type = 23;
	public static $dio_general_opcda_elec_meter_dio_type = 31;
	public static $dio_aem_drb_elec_meter_dio_type = 29;
	public static $dio_weema_1p_elec_meter_dio_type = 24;
	public static $dio_weema_3p_elec_meter_dio_type = 25;
	public static $dio_vmr_mp7_elec_meter_dio_type = 21;
	public static $dio_vmr_mp8_elec_meter_dio_type = 45;
	public static $dio_m4m_elec_meter_dio_type = 50;
	public static $dio_jetec_soil_meter_dio_type = 17;
	public static $dio_jnc_temp_humidity_dio_type = 18;
	public static $dio_irti_iva_person_counting_dio_type = 26;
	public static $dio_temp_tcs30a22 = 48;
	public static $dio_temp_tcs5282 = 49;
	public static $dio_irti_iva_person_detection_dio_type = 27;
	public static $dio_co2_dio_type = 22;
	public static $dio_weema_iaq_dio_type = 28;
	public static $msgtypes = array(

		1=>'簡訊',
		2=>'email',

	);

	public static $elec_types = array(

		1=>'住家',
		2=>'公共',

	);

	public static $dio_vendors = array(

		1=>'weema',
		2=>'研華',
		3=>'昕暘',
		4=>'SOYAL',
		5=>'泓格',
		6=>'weema 485',
		7=>'ModbusTCP(Coil for DI/DO)',
		8=>'六川電梯',
		9=>'永揚消防',
		10=>'保創消防',
		11=>'三菱電梯',
		13=>'大業電梯',
		101=>'door',
		999=>'Clone Nodes'

	);

	public static $port_vendors = array(

		5801=>'5801',
		5809=>'5809',
		5811=>'5811',
		501=>'501',
		502=>'502',
		503=>'503',
		504=>'504',
		1631=>'1631',
		5020=>'5020',
		1621=>'1621',
		1623=>'1623'
	);

	public static $rs485_vendors = array(

		1=>'weema',

	);
	public static $rs485_devices = array(

		4=>'温濕度計',
		5=>'電表',
		6=>'水表',
		7=>'一氧化碳',
		8=>'智慧路燈',
		9=>'巧力電表',
		10=>'大同電表',
		11=>'永嘉溫濕度計',
		12=>'Bender PEM333 電表',
		13=>'Bender PEM575 電表',
		14=>'士林電機電表',
		15=>'久德風向感測器',
		16=>'久德雨量計',
		17=>'久德土壤計',
		18=>'JNC智慧空氣偵測儀',
		19=>'新望太陽能',
		20=>'台科電水表',
		21=>"VMR MP7電表",
		22=>"二氧化碳",
		23=>'沙崙OPCDA電表',
		31=>'通用電表',
		32=>'通用水表',
		24=>'Weema Outlet 1P (Ehome)',
		25=>'Weema Outlet 3P (Ehome)',
		26=>'IRTI人型辨識',
		27=>'IRTI人流',
		28=>'Weema IAQ',
		29=>'AEM DRB電表',
		30=>'屏榮空調AI',
		33=>'士林電機SPM8(太陽能)',
		34=>'通用一氧化碳',
		35=>'通用AI（勿與非通用AI混用）',
		36=>'巧力BAW-1A2A',
		37=>"通用太陽能",
		38=>'永嘉溫濕度計-TCSGD1000',
		39=>'永嘉溫濕度計三合一-TCS3001C-1-N-N-N-C-N',
		40=>'永嘉CO-TCS3001C-N1NNCN',
		41=>'永嘉PM-TCS3001C-1NNNCN',
		42=>'永嘉溫度照度計',
		43=>'Acuvim電表',
		44=>'DAE PM210電表',
		45=>'VMR MP8電表',
		46=>'永嘉溫濕度+CO-TCS3001C',
		50=>'M4M電表',
		51=>'KT-MK3電表',
		47=>'巧力BAW-2C',
		48=>'空氣品質TCS30A22',
		49=>'CO2 TCS5282',
	);
	public static $rs485_main = array(

		0=>'否',
		1=>'是',

	);

	public static $dio_ports = array(

		1=>'5801',
		2=>'502',
		3=>'502',
	);

	public static $dio_types = array(

		1=>'DI',
		2=>'DO',
		3=>'AI',
	);

	public static $dio_types_value = array(

		1=>1,
		2=>0,
	);

	public static $dio_values = array(

		0=>'關',
		1=>'開',
	);

	public static $fontsizes = array(

		3=>'3',
		4=>'4',
		5=>'5',
		6=>'6',
		7=>'7',
		8=>'8',
		9=>'9',
		10=>'10',

	);

	public static $colors = array(

		'black'=>'黑',
		'red'=>'紅',
		'orange'=>'橙',
		'yellow'=>'蕢',
		'green'=>'綠',
		'blue'=>'藍',
		'indigo'=>'靛',
		'purple'=>'紫',

	);

	public static $msg_types = array(

		1=>'簡訊(文字)',
		2=>'簡訊(語音)',
		3=>'APP通知(softphone)',
		4=>'電子郵件',
		5=>'LINE',

	);

	public static $liuchuanElevatorIODefinition;
	public static $mitsubishiElevatorIODefinition;
	public static $fujiElevatorIODefinition;



	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */
	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_top/models/' . strtolower($name) . '.php'))
		{
			require_once JPATH_SITE . '/components/com_top/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'TopModel');
		}

		return $model;
	}

public static function get_my_group($type)
{
	$ret = 0;
	if($type > 1000)
	{
		$type = $type-1000;
		$items = self::getGroupItem($type,6);
		if(count($items))
		{
			$items1 = self::getGroupItem($items[0]->type,7);

			if(count($items1))
			{
				$ret = $items1[0]->id;
			}
		}

	}
	else
	{
		$items1 = self::getGroupItem(0,8);
		if(count($items1))
		{
			$ret = $items1[0]->id;
		}
	}

	//JLog::add($ret, JLog::INFO, 'jerror');

	return $ret;

}

	public static function get_all_group($id=0)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__top_table` AS a');

		if($id > 0)
		    $query->where('a.id = '.$id);

		$query->where('a.state = 1');
		$query->where('a.note1 = '.self::$all_group);

		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
     //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;
	}

	public static function get_all_type($id)
	{
			$items = self::getDIOType($id);

      return $items;
	}
  public static function getIPType()
	{
			$items = self::do_getIPType();
			//$items1 = self::getDIOType();
			foreach($items as $i=>$item)
			{
				$item->id = $item->type;
			}

      return $items;
	}
	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

    /**
     * Gets the edit permission for an user
     *
     * @param   mixed  $item  The item
     *
     * @return  bool
     */
    public static function canUserEdit($item)
    {
        $permission = false;
        $user       = Factory::getUser();

        if ($user->authorise('core.edit', 'com_top'))
        {
            $permission = true;
        }
        else
        {
            if (isset($item->created_by))
            {
                if ($user->authorise('core.edit.own', 'com_top') && $item->created_by == $user->id)
                {
                    $permission = true;
                }
            }
            else
            {
                $permission = true;
            }
        }

        return $permission;
    }

		public static function getDIOType($type)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

		    $query
				   ->select('DISTINCT a.*');

		    $query->from('`#__top_table` AS a');

		    $query->where('a.state = 1');
		    $query->where('a.note1 = '.self::$tdiopage );
        $query->where('a.type = '.$type);
		     //JLog::add($query, JLog::INFO, 'jerror');
		     $db->setQuery($query);

		     $items = (array) $db->loadObjectList();

		     return $items;

		}
		public static function do_getIPType()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

		    $query
				   ->select('DISTINCT a.*');

		    $query->from('`#__top_table` AS a');

		    $query->where('a.state = 1');
		    $query->where('a.note1 = '.self::$iptype);

		     //JLog::add($query, JLog::INFO, 'jerror');
		     $db->setQuery($query);

		     $items = (array) $db->loadObjectList();

		     return $items;

		}
		public static function issame_title_and_port($obj)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.$obj->note1);

			 $query->where('a.port = "'.$obj->port . '"');
			 $query->where('a.name = "'.$obj->name . '"');

       //JLog::add($query, JLog::INFO, 'jerror');
		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

       if(count($items) > 0)
			     return true;
		   else
			     return false;


		}

		public static function issame_title($obj)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.$obj->note1);

			 $query->where('a.name = "'.$obj->name . '"');

       //JLog::add($query, JLog::INFO, 'jerror');
		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

       if(count($items) > 0)
			     return true;
		   else
			     return false;


		}
		public static function update_rs485dir_enable($obj)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__top_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

		}
		public static function dir_save_item()
	  {
			$app = JFactory::getApplication();

			$cid = $app->input->get('device_enable', array(), 'array');
	    $name_id = $app->input->get('name_id', array(), 'array');

	    $obj = new stdClass;

			foreach($name_id as $i=>$item)
			{
				  //JLog::add($item, JLog::WARNING, 'jerror');
	        $obj->id = $item;
					$obj->enable = $cid[$i];

	        self::update_top_enable($obj);

			}

			{

				TopHelpersUtility::send_reload_center();

	        JLog::add(JText::_('COM_TOP_CHANGE_OK'), JLog::WARNING, 'jerror');
	    }

	  }
		public static function update_top_door($obj)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),
				$db->quoteName('name') . ' = ' . $db->quote($obj->name),
				$db->quoteName('path') . ' = ' . $db->quote($obj->path),

				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__top_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

		}

		public static function update_top($obj)
		{
			TopHelpersTop::update_top_and_dio_type($obj, true);
		}
		public static function update_top_and_dio_type($obj, $update_dio_type)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),
				$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type),
				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__top_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();
			if ($update_dio_type) {
				self::update_top_device($obj);
			}
		}

		public static function update_top_device($obj)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type),
				//$db->quoteName('main') . ' = ' . $db->quote($obj->main),

			);

			$conditions = array(
					$db->quoteName('dio_id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

		}
		public static function update_top_enable($obj)
		{
			$db = JFactory::getDbo();

			$fields = array(

				$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),

			);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__top_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

		}
		public static function delete_rs485dir_item($id)
		{
        self::delete_rs485_group($id);
		    self::delete_item($id);
		}
		public static function delete_alarmdir_item($id)
		{
		    self::delete_dio_alarm_item($id);
        self::delete_alarm_item($id);
		    self::delete_item($id);
		}
		public static function delete_conddir_item($id)
		{
        self::delete_alarm_item($id);
		    self::delete_item($id);
		}
		public static function delete_typedir_item($id)
		{
		    //self::delete_dio_alarm_item($id);
        //self::delete_alarm_item($id);
		    //self::delete_item($id);
		}
		public static function getAlarmItem($id)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

       $query->where('a.state = 1');
			 $query->where('a.note1 = '.self::$apage);
       $query->where('a.alarm = '.$id);


		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function delete_dio_alarm_item($id)
    {
	      $items = self::getAlarmItem($id);

				foreach($items as $i=>$item)
				{
					self::do_delete_dio_alarm_item($item->id);
				}
    }

		public static function do_delete_dio_alarm_item($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('group') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__dio_alarm_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_alarm_item($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('alarm') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__top_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_top_item($id)
		{
			  $items = self::getGroupItem($id,4);

				if(count($items))
				{
					if($items[0]->iscondition)
					{
						self::sendDIOCondition();
					}
				}
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('id') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__top_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}
		public static function delete_rs485_group($id)
		{
				$db = JFactory::getDbo();

				// Create a new query object.
				$query = $db->getQuery(true);

				$conditions = array(
						$db->quoteName('group') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__device_table'));
				$query->where($conditions);

				$db->setQuery($query);

				$db->execute();
				//JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_door_top($id)
		{
        $items = self::get_top_item($id);

				if(count($items))
				{
				    self::delete_door_devices($items[0]->id);

				}

				self::delete_item($id);

		}

		public static function delete_door_devices($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('dio_id') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__device_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_door_device($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('id') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__device_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_rs485_item($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('id') . ' = ' . $id,
				);

				$query->delete($db->quoteName('#__device_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}
		public static function find_subitem($item)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__top_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.parent = '.$item);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

		}

		public static function delete_item($obj)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
			      $db->quoteName('id') . ' = ' . $obj,
				);

				$query->delete($db->quoteName('#__top_table'));
				$query->where($conditions);

				$db->setQuery($query);

	      $db->execute();
	      //JLog::add($query, JLog::WARNING, 'jerror');


		}

		public static function delete_floor_item($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // delete all custom keys for user 1001.
	      $conditions = array(
					  $db->quoteName('id') . ' = ' . $id);


	      $query->delete($db->quoteName('#__floor_table'));
	      $query->where($conditions);

	      $db->setQuery($query);
	      $db->execute();

		}



		public static function delete_floor($obj)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // delete all custom keys for user 1001.
	      $conditions = array(
					  $db->quoteName('group') . ' = ' . $obj);


	      $query->delete($db->quoteName('#__floor_table'));
	      $query->where($conditions);

	      $db->setQuery($query);
	      $db->execute();

		}

		public static function delete_device_by_floor($id)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
						$db->quoteName('floor') . ' = ' . $db->quote($id),
            $db->quoteName('dio_id') . '= ' . $db->quote(0),

				);

	      $query->delete($db->quoteName('#__device_table'));
	      $query->where($conditions);

	      $db->setQuery($query);
	      $db->execute();

		  TopHelpersUtility::send_reload_center();

		}

		public static function delete_device($obj)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

				$conditions = array(
						$db->quoteName('group') . ' = ' . $db->quote($obj),
						$db->quoteName('dio_id') . '= ' . $db->quote(0),

				);

	      $query->delete($db->quoteName('#__device_table'));
	      $query->where($conditions);

	      $db->setQuery($query);
	      $db->execute();

		}

		public static function delete_dio_device($obj)
		{
		    $db = JFactory::getDbo();

	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // delete all custom keys for user 1001.
	      $conditions = array(
					  $db->quoteName('dio_id') . ' = ' . $obj);

	      $query->delete($db->quoteName('#__device_table'));
	      $query->where($conditions);

	      $db->setQuery($query);
	      $db->execute();

		}

		public static function getRS485Device($id)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

			 $query->where('a.group = '.$id);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}


		public static function getDevice($id)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       if($id <= 0)
			     $query->where('a.dio_id = -1');
			 else
			     $query->where('a.dio_id = '.$id);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function add_new_dio_item($obj)
    {
	      $db = JFactory::getDbo();
	      $db->setQuery('SELECT MAX(ordering) FROM #__top_table');
	      $max = $db->loadResult();
	      $ordering = $max+1;

	      $created_by = JFactory::getUser()->name;
	      $checked_out = false;
	      $state = 1;

        $note1 = self::$dpage;
	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // Insert columns.
	      $columns = array(
				    'ordering',
				    'state',
				    'checked_out',
				    'created_by',
				    'note1',
						'parent',
				    'name',
            'device_id',
						'addr',
						'dio_type',
						'port',
						'vendor',
						'ipaddr',
						'enable',

	      );

	      // Insert values.
	      $values = array(
				    $db->quote($ordering),
				    $db->quote($state),
				    $db->quote($checked_out),
				    $db->quote($created_by),
						$db->quote($note1),
				    $db->quote($obj->myid1),
				    $db->quote($obj->name),

						$db->quote($obj->device_id),
				    $db->quote($obj->addr),
						$db->quote($obj->dio_type),
				    $db->quote($obj->port),
						$db->quote($obj->vendor),
						$db->quote($obj->ipaddr),
						$db->quote($obj->enable),

	      );

				// Prepare the insert query.
	      $query
	          ->insert($db->quoteName('#__top_table'))
	          ->columns($db->quoteName($columns))
	          ->values(implode(',', $values));

	      // Set the query using our newly populated query object and execute it.
	      $db->setQuery($query);
	      $db->execute();

				$db->setQuery('SELECT MAX(id) FROM #__top_table');
		    $max = $db->loadResult();

	      $obj->myid = $max;

    }
		public static function add_new_door_item($obj)
    {
	      $db = JFactory::getDbo();
	      $db->setQuery('SELECT MAX(ordering) FROM #__top_table');
	      $max = $db->loadResult();
	      $ordering = $max+1;

	      $created_by = JFactory::getUser()->name;
	      $checked_out = false;
	      $state = 1;

        $note1 = self::$doornode;
	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // Insert columns.
	      $columns = array(
				    'ordering',
				    'state',
				    'checked_out',
				    'created_by',
				    'note1',
						'parent',
				    'name',
						'enable',
						'path',

	      );

	      // Insert values.
	      $values = array(
				    $db->quote($ordering),
				    $db->quote($state),
				    $db->quote($checked_out),
				    $db->quote($created_by),
						$db->quote($note1),
				    $db->quote($obj->myid1),
				    $db->quote($obj->name),

						$db->quote($obj->enable),
						$db->quote($obj->path),

	      );

				// Prepare the insert query.
	      $query
	          ->insert($db->quoteName('#__top_table'))
	          ->columns($db->quoteName($columns))
	          ->values(implode(',', $values));

	      // Set the query using our newly populated query object and execute it.
	      $db->setQuery($query);
	      $db->execute();

				$db->setQuery('SELECT MAX(id) FROM #__top_table');
		    $max = $db->loadResult();

	      $obj->myid = $max;

    }
		public static function init_new_device($obj)
		{
			$cnt = $obj->port;
			if ($obj->vendor == TopHelpersTop::$dio_yunyangFireFighter_vendor ||
				$obj->vendor == TopHelpersTop::$dio_baochungFireFighter_vendor
			)
			{
				$obj->dio_type = 1;
				// $obj->device_id = $obj->addr;
				// $cnt = 32;
				// $obj->port = 32;
			}
			if($obj->vendor == TopHelpersTop::$dio_soyal_vendor)
			{
				foreach(range(1,2) as $i)
				{
					if ($i == 1)
					{
						$obj->dio_type = 2;
					}
					if ($i == 2)
					{
						$obj->dio_type = 1;
					}
					$obj->dio_id = $obj->myid;
					$obj->index = $i;
					$obj->dio_value = 0;

					$obj->info = $obj->name;
					$obj->note = '';
					$obj->group = 0;
					$obj->supergroup = 0;
					$obj->width = 0;
					$obj->height = 0;

					$obj->type = 0;
					$obj->floor = 0;
					$obj->dio_alarm = 0;
					$obj->path='';
					self::add_device($obj);
				}

			}
			else if ($obj->vendor == TopHelpersTop::$dio_fujiElevator_vendor)
			{
				foreach (range(1,$cnt) as $i)
				{
					foreach (TopHelpersTop::$fujiElevatorIODefinition as $ioItem)
					{
						$obj->dio_id = $obj->myid;
						$obj->index = $i;
						//$obj->dio_type = 1;
						$obj->dio_value = 0;

						$obj->info = $obj->name;
						$obj->note = '';
						$obj->group = 0;
						$obj->supergroup = 0;
						  $obj->width = 0;
						$obj->height = 0;

						$obj->type = 0;
						$obj->floor = 0;
						$obj->dio_alarm = 0;
						  $obj->path='';

						$obj->dio_type = $ioItem->dio_type;
						$obj->note = $ioItem->name;
						$obj->info = $obj->name . '-' . $ioItem->name;
						$obj->index = $ioItem->index;
						$obj->nc = $ioItem->nc;
						self::add_device($obj);
					}

				}
			}
			else if ($obj->vendor == TopHelpersTop::$dio_mitsubishiElevator_vendor)
			{
				foreach (range(1,$cnt) as $i)
				{
					foreach (TopHelpersTop::$mitsubishiElevatorIODefinition as $ioItem)
					{
						$obj->dio_id = $obj->myid;
						$obj->index = $i;
						//$obj->dio_type = 1;
						$obj->dio_value = 0;

						$obj->info = $obj->name;
						$obj->note = '';
						$obj->group = 0;
						$obj->supergroup = 0;
						  $obj->width = 0;
						$obj->height = 0;

						$obj->type = 0;
						$obj->floor = 0;
						$obj->dio_alarm = 0;
						  $obj->path='';

						$obj->dio_type = $ioItem->dio_type;
						$obj->note = $ioItem->name;
						$obj->info = $obj->name . '-' . $ioItem->name;
						$obj->index = $ioItem->index;
						$obj->nc = $ioItem->nc;
						self::add_device($obj);
					}

				}
			}
			else if ($obj->vendor == TopHelpersTop::$dio_liuchuanElevator_vendor)
			{
				foreach (range(1,$cnt) as $i)
				{
					foreach (TopHelpersTop::$liuchuanElevatorIODefinition as $ioItem)
					{
						$obj->dio_id = $obj->myid;
						$obj->index = $i;
						//$obj->dio_type = 1;
						$obj->dio_value = 0;

						$obj->info = $obj->name;
						$obj->note = '';
						$obj->group = 0;
						$obj->supergroup = 0;
						  $obj->width = 0;
						$obj->height = 0;

						$obj->type = 0;
						$obj->floor = 0;
						$obj->dio_alarm = 0;
						  $obj->path='';

						$obj->dio_type = $ioItem->dio_type;
						$obj->note = $ioItem->name;
						$obj->info = $obj->name . '-' . $ioItem->name;
						$obj->index = $ioItem->index;
						$obj->nc = $ioItem->nc;
						self::add_device($obj);
					}

				}
			} else {
				if ($obj->dio_type == TopHelpersTop::$dio_tatung_elec_meter_dio_type)
				{
					$cnt = 12;
				}
				else if ($obj->dio_type == TopHelpersTop::$dio_jetec_soil_meter_dio_type)
				{
					$cnt = 6;
				}
				else if ($obj->dio_type == TopHelpersTop::$dio_aem_drb_elec_meter_dio_type)
				{
					$cnt = 2;
				}
				foreach (range(1,$cnt) as $i)
				{
						$obj->dio_id = $obj->myid;
						if($obj->dio_type == TopHelpersTop::$dio_tatung_elec_meter_dio_type
						|| $obj->dio_type == TopHelpersTop::$dio_aem_drb_elec_meter_dio_type)
						{
							$obj->index = $i;
						}
						elseif ($obj->vendor == TopHelpersTop::$dio_weema_485_vendor )
						{
							if ($obj->dio_type == TopHelpersTop::$dio_jetec_soil_meter_dio_type)
							{
								$obj->index = $i;
							}
							else
							{
								$obj->index = 0;
							}

						}
						else
							$obj->index = $i;
						//$obj->dio_type = 1;
						$obj->dio_value = 0;

				$obj->info = $obj->name;
				$obj->note = '';
				$obj->group = 0;
				$obj->supergroup = 0;
				  $obj->width = 0;
						$obj->height = 0;

						$obj->type = 0;
						$obj->floor = 0;
						$obj->dio_alarm = 0;
			  $obj->path='';

						self::add_device($obj);

				}
			}


		}

		public static function add_door_device($obj)
		{
			    $obj->dio_id = $obj->myid;
		    //$obj->dio_type = 1;
			    $obj->dio_value = 0;

			    $obj->group = 0;
			    $obj->supergroup = 0;
			    $obj->width = 0;
			    $obj->height = 0;

			    $obj->floor = 0;
			    $obj->dio_alarm = 0;

					self::add_device($obj);

		}

		public static function change_old_floor_item($obj)
    {
	      $db = JFactory::getDbo();

				$fields = array(

				    $db->quoteName('name') . ' = ' . $db->quote($obj->name),
				    $db->quoteName('desc') . ' = ' . $db->quote($obj->desc),
						$db->quoteName('elec_type') . ' = ' . $db->quote($obj->elec_type),

			  );

				$conditions = array(
						$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				);

	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__floor_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();
        //JLog::add($query, JLog::INFO, 'jerror');
    }
    public static function add_new_floor_item($obj)
    {
	      $db = JFactory::getDbo();
	      $db->setQuery('SELECT MAX(ordering) FROM #__floor_table');
	      $max = $db->loadResult();
	      $ordering = $max+1;

	      $created_by = JFactory::getUser()->name;
	      $checked_out = false;
	      $state = 1;

	      // Create a new query object.
	      $query = $db->getQuery(true);

	      // Insert columns.
	      $columns = array(
				    'ordering',
				    'state',
				    'checked_out',
				    'created_by',
				    'building',
				    'name',
						'desc',
						'elec_type',


	      );

	      // Insert values.
	      $values = array(
				    $db->quote($ordering),
				    $db->quote($state),
				    $db->quote($checked_out),
				    $db->quote($created_by),
				    $db->quote($obj->myid),
				    $db->quote($obj->name),
						$db->quote($obj->desc),
						$db->quote($obj->elec_type),


	      );

				// Prepare the insert query.
	      $query
	          ->insert($db->quoteName('#__floor_table'))
	          ->columns($db->quoteName($columns))
	          ->values(implode(',', $values));

	      // Set the query using our newly populated query object and execute it.
	      $db->setQuery($query);
	      $db->execute();

    }

		public static function add_new_alarm_item($obj)
    {

	      $db = JFactory::getDbo();
	      $db->setQuery('SELECT MAX(ordering) FROM #__top_table');
	      $max = $db->loadResult();
	      $ordering = $max+1;

	      $created_by = JFactory::getUser()->name;
	      $checked_out = false;
	      $state = 1;

        $note1 = self::$apage;
	      // Create a new query object.
	      $query = $db->getQuery(true);

				//$iscondition = 0;
				$timeout_di_c = 1;
				$dio_value_do_c = 1;
				$timeout_do_c = 1;
				$dio_value_do_cs = 0;
				$timeout_do_cs = 0;

	      // Insert columns.
	      $columns = array(
				    'ordering',
				    'state',
				    'checked_out',
				    'created_by',
				    'alarm',
				    'name',
						'note1',

						'iscondition',
						'timeout_di_c',
						'dio_value_do_c',
						'timeout_do_c',
						'dio_value_do_cs',
						'timeout_do_cs',
	      );

	      // Insert values.
	      $values = array(
				    $db->quote($ordering),
				    $db->quote($state),
				    $db->quote($checked_out),
				    $db->quote($created_by),
				    $db->quote($obj->myid),
				    $db->quote($obj->name),
						$db->quote($note1),
            $db->quote($obj->iscondition),
						$db->quote($timeout_di_c),
						$db->quote($dio_value_do_c),
						$db->quote($timeout_do_c),
						$db->quote($dio_value_do_cs),
            $db->quote($timeout_do_cs),

	      );

				// Prepare the insert query.
	      $query
	          ->insert($db->quoteName('#__top_table'))
	          ->columns($db->quoteName($columns))
	          ->values(implode(',', $values));

	      // Set the query using our newly populated query object and execute it.
	      $db->setQuery($query);
	      $db->execute();

    }

		public static function add_new_rs485_item($obj)
    {
			$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__device_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

      // Create a new query object.
      $query = $db->getQuery(true);

      // Insert columns.
      $columns = array(
            'ordering',
            'state',
            'checked_out',
            'created_by',

						'type',
            'info',
            'note',
						'update',
						'update_time',
						'group',
						'supergroup',
						'main',
						'brand',
						'enable',
						'addr',
						'dio_type',
            'floor',
						'dio_id',
						'width',
						'height',
						'pointx',
						'pointy',
						'status',
      );

      // Insert values.
      $values = array(
            $db->quote($ordering),
            $db->quote($state),
            $db->quote($checked_out),
            $db->quote($created_by),

						$db->quote($obj->type),
						$db->quote($obj->info),
						$db->quote($obj->note),

						$db->quote($update),
						$db->quote($update_time),
						$db->quote($obj->group),
						$db->quote($obj->supergroup),
						$db->quote($obj->main),
						$db->quote($obj->brand),
						$db->quote($obj->enable),
						$db->quote($obj->addr),
						$db->quote($obj->dio_type),
            $db->quote($obj->floor),
						$db->quote(0),
						$db->quote(0),
						$db->quote(0),
						$db->quote(0),
						$db->quote(0),
						$db->quote(0),

      );

      // Prepare the insert query.
      $query
          ->insert($db->quoteName('#__device_table'))
          ->columns($db->quoteName($columns))
          ->values(implode(',', $values));

      // Set the query using our newly populated query object and execute it.
      $db->setQuery($query);
      $db->execute();
    }

		public static function add_new_cctv_item($obj)
    {

	      $db = JFactory::getDbo();
	      $db->setQuery('SELECT MAX(ordering) FROM #__top_table');
	      $max = $db->loadResult();
	      $ordering = $max+1;

	      $created_by = JFactory::getUser()->name;
	      $checked_out = false;
	      $state = 1;

        $note1 = self::$tvpage;
	      // Create a new query object.
	      $query = $db->getQuery(true);



	      // Insert columns.
	      $columns = array(
				    'ordering',
				    'state',
				    'checked_out',
				    'created_by',
				    'alarm',
				    'name',
						'note1',


	      );

	      // Insert values.
	      $values = array(
				    $db->quote($ordering),
				    $db->quote($state),
				    $db->quote($checked_out),
				    $db->quote($created_by),
				    $db->quote($obj->myid),
				    $db->quote($obj->name),
						$db->quote($note1),


	      );

				// Prepare the insert query.
	      $query
	          ->insert($db->quoteName('#__top_table'))
	          ->columns($db->quoteName($columns))
	          ->values(implode(',', $values));

	      // Set the query using our newly populated query object and execute it.
	      $db->setQuery($query);
	      $db->execute();

    }

		public static function add_device($obj)
		{
			$dio_id = $obj->dio_id;
			$index = $obj->index;
			$dio_type = $obj->dio_type;
			$dio_value = $obj->dio_value;
			$floor = $obj->floor;
			$group = $obj->group;
      $width = $obj->height;
			$height = $obj->height;

			$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__device_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;
	    $enable = 0;

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

      // Create a new query object.
      $query = $db->getQuery(true);

      // Insert columns.
      $columns = array(
            'ordering',
            'state',
            'checked_out',
            'created_by',
            'floor',
            'index',
            'pointx',
            'status',
						'group',
						'width',
						'height',
						'type',
						'enable',
						'dio_id',
						'dio_type',
						'dio_value',
            'info',
            'note',
						'dio_alarm',
						'dio_alarmdir',
						'fontsize',
						'color',
						'update',
						'update_time',
						'green',
						'yellow',
						'red',
						'supergroup',
						'path',

      );

      $fontsize = 3;
			$color = 'black';
      // Insert values.
      $values = array(
            $db->quote($ordering),
            $db->quote($state),
            $db->quote($checked_out),
            $db->quote($created_by),
            $db->quote($floor),
            $db->quote($index),
            $db->quote(-1),
            $db->quote(1),
						$db->quote($group),
						$db->quote($width),
						$db->quote($height),
						$db->quote($obj->type),
						$db->quote($enable),
						$db->quote($dio_id),
						$db->quote($dio_type),
						$db->quote($dio_value),

						$db->quote($obj->info),
						$db->quote($obj->note),
						$db->quote($obj->dio_alarm),
						$db->quote($obj->dio_alarmdir),
						$db->quote($fontsize),
						$db->quote($color),
						$db->quote($update),
						$db->quote($update_time),
						$db->quote($obj->green),
						$db->quote($obj->yellow),
						$db->quote($obj->red),
						$db->quote($obj->supergroup),
						$db->quote($obj->path),

      );

      // Prepare the insert query.
      $query
          ->insert($db->quoteName('#__device_table'))
          ->columns($db->quoteName($columns))
          ->values(implode(',', $values));

      // Set the query using our newly populated query object and execute it.
      $db->setQuery($query);
      $db->execute();

    }

		public static function change_rs485_device($obj)
		{
			//JLog::add($obj->id . ' ' . $obj->dio_type, JLog::INFO, 'jerror');
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

	      $db = JFactory::getDbo();

				$fields = array(

					$db->quoteName('info') . ' = ' . $db->quote($obj->info),
					$db->quoteName('note') . ' = ' . $db->quote($obj->note),
					$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),
          $db->quoteName('addr') . ' = ' . $db->quote($obj->addr),
					$db->quoteName('type') . ' = ' . $db->quote($obj->type),
					$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type),
	        $db->quoteName('floor') . ' = ' . $db->quote($obj->floor),

					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time),

				);

				$conditions = array(
						$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();


		}
		public static function update_floor_item($obj)
		{

	      $db = JFactory::getDbo();

				$fields = array(


					$db->quoteName('floor') . ' = ' . $db->quote(66),

				);

				$conditions = array(
						$db->quoteName('floor') . ' = ' . $db->quote(6),

				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

		}

		public static function change_door_device($obj)
		{
			//JLog::add($obj->id . ' ' . $obj->dio_type, JLog::INFO, 'jerror');
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");


	      $db = JFactory::getDbo();

				$fields = array(

					$db->quoteName('info') . ' = ' . $db->quote($obj->info),
					$db->quoteName('note') . ' = ' . $db->quote($obj->note),

					$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type),

					$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),

					$db->quoteName('path') . ' = ' . $db->quote($obj->path),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time),

				);

				$conditions = array(
						$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

		}

    public static function change_old_device($obj)
		{
			//JLog::add($obj->id . ' ' . $obj->dio_type, JLog::INFO, 'jerror');
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");


	      $db = JFactory::getDbo();

				$fields = array(
					$db->quoteName('dio_type') . ' = ' . $db->quote($obj->dio_type),
					$db->quoteName('dio_value') . ' = ' . $db->quote($obj->dio_value),
					$db->quoteName('nc') . ' = ' . $db->quote($obj->nc),
					$db->quoteName('info') . ' = ' . $db->quote($obj->info),
					$db->quoteName('note') . ' = ' . $db->quote($obj->note),

					$db->quoteName('group') . ' = ' . $db->quote($obj->group),
					$db->quoteName('type') . ' = ' . $db->quote($obj->type),

					$db->quoteName('floor') . ' = ' . $db->quote($obj->floor),
					$db->quoteName('dio_alarm') . ' = ' . $db->quote($obj->dio_alarm),
					$db->quoteName('dio_alarmdir') . ' = ' . $db->quote($obj->dio_alarmdir),

					$db->quoteName('enable') . ' = ' . $db->quote($obj->enable),

					$db->quoteName('fontsize') . ' = ' . $db->quote($obj->fontsize),
					$db->quoteName('color') . ' = ' . $db->quote($obj->color),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time),
					$db->quoteName('green') . ' = ' . $db->quote($obj->green),
					$db->quoteName('yellow') . ' = ' . $db->quote($obj->yellow),
					$db->quoteName('red') . ' = ' . $db->quote($obj->red),
					$db->quoteName('supergroup') . ' = ' . $db->quote($obj->supergroup),
					$db->quoteName('show_node_text') . ' = ' . $db->quote($obj->show_node_text),
					$db->quoteName('trigger_alarm') . ' = ' . $db->quote($obj->trigger_alarm),
					$db->quoteName('is_fault_node') . ' = ' . $db->quote($obj->is_fault_node),
					$db->quoteName('display_last_alarm_time') . ' = ' . $db->quote($obj->display_last_alarm_time),
					$db->quoteName('ba_app_enable') . ' = ' . $db->quote($obj->ba_app_enable),
					$db->quoteName('alarm_threshold') . ' = ' . $db->quote($obj->alarm_threshold),
					$db->quoteName('temperature_alarm_threshold') . ' = ' . $db->quote($obj->temperature_alarm_threshold),
					$db->quoteName('temperature_alarmdir') . ' = ' . $db->quote($obj->temperature_alarmdir),
					$db->quoteName('temperature_alarm') . ' = ' . $db->quote($obj->temperature_alarm),
					$db->quoteName('lux_alarm_threshold') . ' = ' . $db->quote($obj->lux_alarm_threshold),
					$db->quoteName('lux_alarmdir') . ' = ' . $db->quote($obj->lux_alarmdir),
					$db->quoteName('lux_alarm') . ' = ' . $db->quote($obj->lux_alarm),
					$db->quoteName('humidity_alarm_threshold') . ' = ' . $db->quote($obj->humidity_alarm_threshold),
					$db->quoteName('humidity_alarmdir') . ' = ' . $db->quote($obj->humidity_alarmdir),
					$db->quoteName('humidity_alarm') . ' = ' . $db->quote($obj->humidity_alarm),
					$db->quoteName('co_sensor_alarm_threshold') . ' = ' . $db->quote($obj->co_sensor_alarm_threshold),
					$db->quoteName('co_sensor_alarmdir') . ' = ' . $db->quote($obj->co_sensor_alarmdir),
					$db->quoteName('co_sensor_alarm') . ' = ' . $db->quote($obj->co_sensor_alarm),
					$db->quoteName('co2_ppm_alarm_threshold') . ' = ' . $db->quote($obj->co2_ppm_alarm_threshold),
					$db->quoteName('co2_ppm_alarmdir') . ' = ' . $db->quote($obj->co2_ppm_alarmdir),
					$db->quoteName('co2_ppm_alarm') . ' = ' . $db->quote($obj->co2_ppm_alarm),
					$db->quoteName('pm01_alarm_threshold') . ' = ' . $db->quote($obj->pm01_alarm_threshold),
					$db->quoteName('pm01_alarmdir') . ' = ' . $db->quote($obj->pm01_alarmdir),
					$db->quoteName('pm01_alarm') . ' = ' . $db->quote($obj->pm01_alarm),
					$db->quoteName('pm25_alarm_threshold') . ' = ' . $db->quote($obj->pm25_alarm_threshold),
					$db->quoteName('pm25_alarmdir') . ' = ' . $db->quote($obj->pm25_alarmdir),
					$db->quoteName('pm25_alarm') . ' = ' . $db->quote($obj->pm25_alarm),
					$db->quoteName('pm10_alarm_threshold') . ' = ' . $db->quote($obj->pm10_alarm_threshold),
					$db->quoteName('pm10_alarmdir') . ' = ' . $db->quote($obj->pm10_alarmdir),
					$db->quoteName('pm10_alarm') . ' = ' . $db->quote($obj->pm10_alarm),
					$db->quoteName('tvoc_alarm_threshold') . ' = ' . $db->quote($obj->tvoc_alarm_threshold),
					$db->quoteName('tvoc_alarmdir') . ' = ' . $db->quote($obj->tvoc_alarmdir),
					$db->quoteName('tvoc_alarm') . ' = ' . $db->quote($obj->tvoc_alarm),
					$db->quoteName('hcho_alarm_threshold') . ' = ' . $db->quote($obj->hcho_alarm_threshold),
					$db->quoteName('hcho_alarmdir') . ' = ' . $db->quote($obj->hcho_alarmdir),
					$db->quoteName('hcho_alarm') . ' = ' . $db->quote($obj->hcho_alarm),
				);

				$conditions = array(
						$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();


        $width = 0;
				$fields = array(

					$db->quoteName('width') . ' = ' . $db->quote($obj->width),
					$db->quoteName('height') . ' = ' . $db->quote($obj->height),

				);

				$conditions = array(
						$db->quoteName('id') . ' = ' . $db->quote($obj->id),
						$db->quoteName('width') . ' = ' . $db->quote($width)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

		}

		public static function getGroupItem($mygroup,$note1=1)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.$note1);

       if($mygroup > 0)
			     $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function get_top_item($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__top_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.id = '.$id);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
   }

		public static function get_dio_alarm_top($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__top_table` AS a');

		 $query->where('a.state = 1');
		 $query->where('a.note1 = '.self::$apage);

		 $query->where('a.alarm = '.$id);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
   }

	 public static function get_dio_alarmdir_top()
	 {
		 $db = JFactory::getDbo();
		 $query = $db->getQuery(true);

		 // Select the required fields from the table.
		$query
		->select('DISTINCT a.*');

		$query->from('`#__top_table` AS a');

		$query->where('a.state = 1');
		$query->where('a.note1 = '.self::$adpage);

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}

	public static function get_dio_supergroup()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');

	 $query->where('a.note1 = '.self::$tdpage);

	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;
 }

	 public static function get_dio_group($super)
	 {
		 $db = JFactory::getDbo();
		 $query = $db->getQuery(true);

		 // Select the required fields from the table.
		$query
		->select('DISTINCT a.*');

		$query->from('`#__top_table` AS a');

		$query->where('a.state = 1');

		$query->where('a.note1 = '.self::$tdiopage );

		$query->where('a.type =' .$super);

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}



  public static function get_dio_alarm_item($group_id)
  {
	    $db = JFactory::getDbo();
	    $query = $db->getQuery(true);

	    // Select the required fields from the table.
     $query
     ->select('DISTINCT a.*');

     $query->from('`#__dio_alarm_table` AS a');

     $query->where('a.state = 1');

		 $query->where('a.group = '. $group_id);

     $db->setQuery($query);

     $items = (array) $db->loadObjectList();

     return $items;
  }

	public static function get_dio_parent_top($id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');

	 $query->where('a.note1 = '.self::$ddiopage);

   if($id > 0)
	     $query->where('a.id = '.$id);

	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;
 }

	 public static function get_dio_top_floor()
	 {
		 $db = JFactory::getDbo();
		 $query = $db->getQuery(true);

		 // Select the required fields from the table.
		$query
		->select('DISTINCT a.*');

		$query->from('`#__top_table` AS a');

		$query->where('a.state = 1');

		$query->where('a.note1 = '.self::$bpage);

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}


	public static function get_top_type($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$tdiopage );

	 $query->where($db->quoteName('a.type') . ' = ' . $db->quote($top_id));

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function get_dio_top($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$dpage );

   if($top_id > 0)
	     $query->where($db->quoteName('a.parent') . ' = ' . $db->quote($top_id));

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}
	public static function get_door_top($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$doornode );

   if($top_id > 0)
	     $query->where($db->quoteName('a.parent') . ' = ' . $db->quote($top_id));

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}
	public static function get_rs485_top($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$rs485page );

   if($top_id > 0)
		   $query->where('a.id = '.$top_id );

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function get_top_alarm($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$apage);
	 $query->where('a.alarm = ' . $top_id);

	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function get_rs485_device($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__device_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.group = '.$top_id);


	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function get_top_cctv($top_id)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__top_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.note1 = '.self::$tvpage);
	 $query->where('a.alarm = ' . $top_id);

	 $db->setQuery($query);



	 $items = (array) $db->loadObjectList();

	 return $items;
	}


public static function get_top_floor($top_id)
{
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
 $query
 ->select('DISTINCT a.*');

 $query->from('`#__floor_table` AS a');

 $query->where('a.state = 1');

 $query->where('a.building = ' . $top_id);

 $db->setQuery($query);

 $items = (array) $db->loadObjectList();

 return $items;
}

public static function do_save_rs485_device_file($id)
{
	  $fp = fopen('/opt/24dio/config2.txt', 'w');

		$rs485_items = self::get_rs485_top(0);
		foreach($rs485_items as $i=>$item)
		{
				$items1 = self::getRS485Device($item->id);

				if(count($items1) == 0)    continue;

				fwrite($fp, 'rs485 '.$item->name.' ');
				fwrite($fp, $item->port.' ');
				fwrite($fp, $item->id." ");
				fwrite($fp, $item->vendor.' ');
				fwrite($fp, $item->enable.' ');

				fwrite($fp, count($items1).' ');
				foreach($items1 as $i1=>$item1)
				{
						fwrite($fp, $item1->id.' ');
						fwrite($fp, $item1->dio_type.' ');
						fwrite($fp, $item1->brand.' ');
						fwrite($fp, $item1->addr.' ');
						fwrite($fp, $item1->enable.' ');
				}

				fwrite($fp, "\n");
		}

		fclose($fp);
}

public static function getCenterList()
{
	// Create a new query object.
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	$query
		 ->select('DISTINCT a.*');

	$query->from('`#__urllist_table` AS a');

	$query->where('a.state = 1');
	$query->where('a.type = 2');

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $db->setQuery($query);
	 //echo($query);
	 //JLog::add($query, JLog::INFO, 'jerror');

	 $items = (array) $db->loadObjectList();

	 return $items;
}

    public static function add_device_string($obj)
		{

      if($obj->vendor == self::$dio_soyal_vendor)
			{
				  $this_items = self::getGroupItem($obj->id,TopHelpersTop::$dpage);
				  $str = 'soyal ';

					if(count($this_items))
					{
						  $obj->ipaddr = $this_items[0]->ipaddr;

				  }

					$items = self::getDevice($obj->id);
					if(count($items))
					{
						  $obj->id = $items[0]->id;

				  }
			}
			else
			{
			    $str = 'device ';

		  }

      $str = $str . $obj->ipaddr.' ';

			$items1 = self::getDevice($obj->myid);
			//JLog::add(count($items1), JLog::INFO, 'jerror');
			$str_type = '';
			$str_value = '';
			foreach($items1 as $i1=>$item1)
			{

					$str_type = $str_type.self::$dio_types_value[$item1->dio_type];
					$str_value = $str_value.$item1->dio_value;

			}

			$str = $str .'1 ';
			$str = $str .$str_type.' ';
			$str = $str .$str_value.' ';
			$str = $str .$obj->vendor.' ';
			$str = $str .$obj->addr.' ';
			$str = $str .$obj->device_id.' ';
			$str = $str .$obj->port.' ';
			$str = $str .$obj->id.' ';
      $str = $str .$obj->dio_port.' ';
      $str = $str .$items1[0]->id.' ';

			$str = 'ADD='.$str;//input, transfer msg to server side

			//JLog::add($str, JLog::INFO, 'jerror');

			$len = strlen($str);

			$hex = sprintf('%08X', $len);
			$str = "WA=".$hex.$str;

      self::write_to_socket($str);
		}

		public static function write_to_socket($str)
		{
      //echo $str. "<br>";
			$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

			if ($socket < 0) {
				  JLog::add("---Failed: socket_create() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');
			    //echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
					return;
			}

			//socket_connect
			$result = socket_connect($socket, "/tmp/diosocket2");

			if ($result < 0) {
				  JLog::add("---Failed: socket_connect() failed! Reason: " . socket_strerror($result), JLog::INFO, 'jerror');

			   // echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

	        //Close
	        socket_close($socket);
					return;
			}

			$in = $str;//input, transfer msg to server side

			//socket_write
			if(!socket_write($socket, $in, strlen($in))) {
				  JLog::add("---Failed: socket_write() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');

			    //echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
			}


			//Close
			socket_close($socket);

		}

		public static function del_device_string($id)
		{
      $items = self::get_dio_top($id);

      if(count($items) > 0)
			{
			    $str = 'DEL=device '.$items[0]->id;

					$len = strlen($str);

					$hex = sprintf('%08X', $len);
					$str = "WA=".$hex.$str;

          self::write_to_socket($str);
		  }
		}

		public static function del_device_file()
    {

        self::do_save_device_file();
		}

		public static function do_save_device_file()
    {
			  $parents = self::get_dio_parent_top(0);

        $items = self::get_dio_top(0);

				$fp = fopen('/opt/24dio/config1.txt', 'w');

				foreach($items as $i=>$item)
				{
					  if($item->vendor > 100)    continue;

            if($item->vendor == self::$dio_soyal_vendor)
						{
							fwrite($fp, 'soyal '.$item->ipaddr.' ');
						}
						else
						{
						    fwrite($fp, 'device '.$item->ipaddr.' ');
						}

            $items1 = self::getDevice($item->id);

						$str_type = '';
						$str_value = '';
						foreach($items1 as $i1=>$item1)
						{
							  $str_type = $str_type.self::$dio_types_value[$item1->dio_type];
								$str_value = $str_value.$item1->dio_value;

						}
            fwrite($fp, '1 ');
						fwrite($fp, $str_type.' ');
						fwrite($fp, $str_value.' ');

            $vendor = $item->vendor;
						$dio_port = $item->dio_port ;

            foreach($parents as $i2=>$item2)
						{
							if($item2->id == $item->parent)
							{
								$vendor = $item2->vendor;
								$dio_port = $item2->dio_port;
								break;
							}
						}
						fwrite($fp, $vendor.' ');
						fwrite($fp, $item->addr.' ');
						fwrite($fp, $item->device_id.' ');
						fwrite($fp, $item->port.' ');
						fwrite($fp, $item->id." ");
						fwrite($fp, $dio_port." ");
            fwrite($fp, $items1[0]->id." ");
						fwrite($fp, "\n");


				}

				$rs485_items = self::get_rs485_top(0);
				foreach($rs485_items as $i=>$item)
				{
					  $items1 = self::getRS485Device($item->id);

						if(count($items1) == 0)    continue;

						fwrite($fp, 'rs485 '.$item->name.' ');
						fwrite($fp, $item->port.' ');
						fwrite($fp, $item->id." ");
						fwrite($fp, $item->vendor.' ');
            fwrite($fp, $item->enable.' ');

            fwrite($fp, count($items1).' ');
						foreach($items1 as $i1=>$item1)
						{
							  fwrite($fp, $item1->id.' ');
								fwrite($fp, $item1->dio_type.' ');
								fwrite($fp, $item1->brand.' ');
								fwrite($fp, $item1->addr.' ');
								fwrite($fp, $item1->enable.' ');
						}

            fwrite($fp, "\n");
				}


        $msg = self::getDIOConditionMsg(0);

				fwrite($fp, $msg."\n");

				fclose($fp);
				system('cp /opt/24dio/config1.txt /opt/24dio/config.txt');
    }

    public static function save_device_file($obj)
    {
		TopHelpersUtility::send_reload_center();
			  self::add_device_string($obj);

        self::do_save_device_file();
    }


		public static function update_device_size($id,$iwidth,$iheight)
		{
			JLog::add('size '.$id.' '.$iwidth.' '.$iheight, JLog::INFO, 'jerror');

			$db = JFactory::getDbo();

      $width = 0;
			$fields = array(

				$db->quoteName('width') . ' = ' . $db->quote($iwidth),
				$db->quoteName('height') . ' = ' . $db->quote($iheight),


			);

			$conditions = array(
					$db->quoteName('group') . ' = ' . $db->quote($id),
					$db->quoteName('width') . ' = ' . $db->quote($width),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

		}
		public static function getDIOCondition()
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');


			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$apage);
			$query->where('a.iscondition = 1');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
	     //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;
    }

		public static function getDIOConditionMsg($add_title=1)
	  {
        $items = self::getDIOCondition();

				//$parents = self::get_dio_parent_top(0);

        if(count($items) == 0)
        {
				 $msg = 'NOCONDITION '."\n";
        }
				else
				{
				    $msg = '';
				    foreach($items as $i=>$item)
				    {
							  if($add_title)
								{
								    $msg1 = 'CONDITION='.$item->dio_device_di_c.' ';
								}
								else
								{
                    $msg1 = 'CONDITION '.$item->dio_device_di_c.' ';
							  }

						    $msg1 = $msg1 . $item->dio_index_di_c.' ';
								$msg1 = $msg1 . $item->timeout_di_c.' ';
								$msg1 = $msg1 . $item->timeout_alarm_c.' ';

						    $msg1 = $msg1 .$item->dio_device_do_c.' ';
						    $msg1 = $msg1 .$item->dio_index_do_c.' ';
						    $msg1 = $msg1 .$item->timeout_do_c.' ';
						    $msg1 = $msg1 .$item->dio_value_do_c.' ';

						    $msg1 = $msg1 .$item->dio_device_di_cs.' ';
						    $msg1 = $msg1 . $item->dio_index_di_cs.' ';
						    $msg1 = $msg1 .$item->timeout_di_cs.' ';

						    $msg1 = $msg1 .$item->dio_value_do_cs.' ';
								$msg1 = $msg1 . $item->timeout_alarm_cs.' ';
								$msg1 = $msg1 . $item->dio_ipaddr_alarm_end_c.' ';
								$msg1 = $msg1 . $item->dio_index_alarm_end_c.' ';
								$msg1 = $msg1 . $item->dio_ipaddr_alarm_end_cs.' ';
								$msg1 = $msg1 . $item->dio_index_alarm_end_cs.' ';
								$msg1 = $msg1 . $item->dio_alarm.' ';
								$msg1 = $msg1 . $item->dio_alarm_cs.' ';
								$msg1 = $msg1 . $item->isall.' ';
								$msg1 = $msg1 . $item->hour_start.' ';
								$msg1 = $msg1 . $item->minute_start.' ';
								$msg1 = $msg1 . $item->hour_end.' ';
								$msg1 = $msg1 . $item->minute_end.' ';

								$msg = $msg1."\n".$msg;

								//JLog::add($msg1, JLog::WARNING, 'jerror');

				    }

        }

				return $msg;
	  }
		public static function writeDIOConditionToFile()
		{
			$fp = fopen('/tmp/dioconfig.txt', 'w');

			$msg = self::getDIOConditionMsg(0);

			fwrite($fp, $msg."\n");

			fclose($fp);

		}
		public static function sendDIOCondition()
	  {
		TopHelpersUtility::send_reload_center();

	  }

		public static function do_sendDIOCondition($msg)
	  {
			$desc = $msg;

			//socket_create
			$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

			if ($socket < 0) {
			    echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
          echo("\n");
					return;
			}

			//socket_connect
			$result = socket_connect($socket, "/tmp/diosocket2");

			if ($result < 0) {
			    echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

          echo("\n");
	        //Close
	        socket_close($socket);
					return;
			}

			$in = $desc;//input, transfer msg to server side

			//socket_write
			if(!socket_write($socket, $in, strlen($in))) {
			    echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
          echo("\n");
			}


			//Close
			socket_close($socket);

	  }

		public static  function file_upload($file,$obj)
	  {
			// Clean up filename to get rid of strange characters like spaces etc
			$filename = JFile::makeSafe($file['name']);

	    $ext =  JFile::getExt($filename);
			if(empty($ext))    $ext = $filename;

			$ext = strtolower($ext);

	    if(empty($filename))    return false;

			//$update = date ("Y/m/d");
			$time = date ("YmdHis");

	    $myfilename = $time.'-'.$obj->myid ."-".$obj->save_type.".". $ext;

	    $ds = '/';
			// Set up the source and destination of the file
			$src = $file['tmp_name'];
			$path = JPATH_COMPONENT . $ds . "uploads";
	    $dest1 = $path . $ds . $obj->myid;

	    $dest = $dest1 . $ds . $myfilename;
			$path1 = 'components/com_top/uploads';
	    $img1 = $path1 . $ds . $obj->myid;

			$img = $img1 . $ds . $myfilename;

			// First check if the file has the right extension, we need jpg only
			if ($ext == 'jpg' || $ext == 'png' || $ext == 'swf')
			{

			   // TODO: Add security checks
	       if(JFolder::exists($path) == false)
				 {

					 JFolder::create($path);
				 }

				 if(JFolder::exists($dest1) == false)
				 {

					 JFolder::create($dest1);
				 }

			   if (JFile::upload($src, $dest))
			   {

					 $obj->path = $img;
					 if($obj->save_type == 'green')
					 {

	             $obj->green = $img;
					 }
						else if($obj->save_type == 'yellow')
			         $obj->yellow = $img;
						else if($obj->save_type == 'red')
					     $obj->red = $img;
	         else
					 {
						 JLog::add('no type', JLog::INFO, 'jerror');

					 }
	         $obj->dir = $path;
					 $obj->dest = $dest;

			      // Redirect to a page of your choice
						JLog::add(JText::_('COM_TOP_UPLOAD_OK'), JLog::INFO, 'jerror');
	          return true;
			   }
			   else
			   {
			      // Redirect and throw an error message
						JLog::add(JText::_('COM_TOP_UPLOAD_ERROR'), JLog::WARNING, 'jerror');

			   }

			}
			else
			{
			   // Redirect and notify user file is not right extension
	       JLog::add(JText::_('COM_TOP_EXTENSION_ERROR'). " ".$ext, JLog::WARNING, 'jerror');
			}

			return false;

	  }

}
TopHelpersTop::$fujiElevatorIODefinition = array(
	0 => (object) array(
		'index' => 1,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '故障',
	),
	1 => (object) array(
		'index' => 2,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '運轉',
	),
	2 => (object) array(
		'index' => 3,
		'dio_type' => 3,
		'name' => '樓層',
	)
);
TopHelpersTop::$mitsubishiElevatorIODefinition = array(
	0 => (object) array(
		'index' => 1,
		'dio_type' => 3,
		'name' => '樓層',
	),
	1 => (object) array(
		'index' => 2,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '上行',
	),
	2 => (object) array(
		'index' => 3,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '下行',
	),
	3 => (object) array(
		'index' => 4,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '故障',
	),
	4 => (object) array(
		'index' => 5,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '運轉',
	)
);

TopHelpersTop::$liuchuanElevatorIODefinition = array(
	0 => (object) array(
		'index' => 1,
		'dio_type' => 3,
		'name' => '樓層',
	),
	1 => (object) array(
		'index' => 2,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '上行顯示',
	),
	2 => (object) array(
		'index' => 3,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '下行顯示',
	),
	3 => (object) array(
		'index' => 4,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '運行狀態',
	),
	4 => (object) array(
		'index' => 5,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '安全迴路',
	),
	5 => (object) array(
		'index' => 6,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '轎門鎖',
	),
	6 => (object) array(
		'index' => 7,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '廳門鎖',
	),
	7 => (object) array(
		'index' => 8,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '火災',
	),
	8 => (object) array(
		'index' => 9,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '地震',
	),
	9 => (object) array(
		'index' => 10,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '滿載',
	),
	10 => (object) array(
		'index' => 11,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '停電',
	),
	11 => (object) array(
		'index' => 12,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '超載',
	),
	12 => (object) array(
		'index' => 13,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '保養中',
	),
	13 => (object) array(
		'index' => 14,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '專用',
	),
	14 => (object) array(
		'index' => 15,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '使用中（開延長）',
	),
	15 => (object) array(
		'index' => 16,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '停用（泊梯）',
	),
	16 => (object) array(
		'index' => 17,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '停用（故障）',
	),
	17 => (object) array(
		'index' => 18,
		'dio_type' => 1,
		'nc' => 0,
		'name' => '緊急電源操作',
	),
	18 => (object) array(
		'index' => 19,
		'dio_type' => 2,
		'name' => '停車控制（泊梯）',
	),
	19 => (object) array(
		'index' => 20,
		'dio_type' => 2,
		'name' => '火災回歸（XF）',
	),
);