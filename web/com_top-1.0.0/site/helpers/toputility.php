<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Top
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2019 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

//JLoader::register('TopHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'toplog.php');

//use \Joomla\CMS\Factory;
//use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

/**
 * Class TopFrontendHelper
 *
 * @since  1.6
 */
class TopHelpersUtility
{
	private static $ip_note1=0;
	private static $sos_note1=1;
	private static $app6_note1=2;
	private static $mail_note1=3;

  	public static $year_type=2;
  	public static $season_type=3;
  	public static $week_type=4;
  	public static $month_type=5;
  	public static $day_type=6;
  	public static $hour_type=7;

  	public static $passwd_error=64;
  	public static $username_error=65;

  	public static $weather_cities = array(

	1 => "宜蘭縣",
	2 => "花蓮縣",
	3 => "臺東縣",
	4 => "澎湖縣",
	5 => "金門縣",
	6 => "連江縣",
	7 => "臺北市",
	8 => "新北市",
	9 => "桃園市",
	10 => "臺中市",
	11 => "臺南市",
	12 => "高雄市",
	13 => "基隆市",
	14 => "新竹縣",
	15 => "新竹市",
	16 => "苗栗縣",
	17 => "彰化縣",
	18 => "南投縣",
	19 => "雲林縣",
	20 => "嘉義縣",
	21 => "屏東縣",
	);

	public static $rs485_temp_device = 4;
	public static $rs485_elec_device = 5;
	public static $rs485_elec_device_cic = 9;
	public static $rs485_elec_device_daepm210 = 44;
	public static $rs485_elec_device_m4m = 50;
	public static $rs485_elec_device_kt_mk3 = 51;
	public static $rs485_elec_device_acuvim = 43;
	public static $rs485_elec_device_tatung = 10;
	public static $rs485_elec_device_pem333 = 12;
	public static $rs485_elec_device_pem575 = 13;
	public static $rs485_elec_device_shihlin = 14;
	public static $rs485_elec_device_cicbaw1a2a = 36;
	public static $rs485_elec_device_cicbaw2c = 47;
	public static $rs485_elec_device_opcda = 23;
	public static $rs485_elec_device_general_opcda = 31;
	public static $rs485_general_opc_water_meter_device = 32;
	public static $rs485_elec_device_aem_drb = 29;
	public static $rs485_elec_device_weema_1p = 24;
	public static $rs485_elec_device_weema_3p = 25;
	public static $rs485_solar_device_primevolt = 19;
	public static $rs485_solar_device_shihlinspm8 = 33;
	public static $rs485_solar_device_general = 37;
	public static $rs485_water_meter_device = 6;
	public static $rs485_tkd_water_meter_device = 20;
	public static $rs485_vmr_mp7_elec_device = 21;
	public static $rs485_vmr_mp8_elec_device = 45;
	public static $rs485_co_device = 6;
	public static $page = 1;

	public static $cctv_type = 3;
	public static $parent_type = 0;
	public static $limit_type = 1;

	public static $parent_bc = 2;
	public static $bc_type = 3;
	private static $SERVER_PORT = 20020;

  public static function my_style()
  {
	  $j3css = <<<ENDCSS
	  .my-div{
		margin:0 auto;
		width:1600px;
		height:100%;
	  }
	  .my-top-div{
		margin-left:0px;
		margin-top:20px;
		width:1400px;
		height:100%;

		border: 1px solid green;
	  }

	  .myinput{
		margin-left:20px;
	  }
	  .myth {

		background-color: #74b2de;
	  }

	  .mymenu
	  {
		  font-size: 18px;

	  }
	  .font-white
	  {
		  color:white;
	  }

ENDCSS;

	  JFactory::getDocument()->addStyleDeclaration($j3css);

	}
	public static function findDeviceVendor($vendor_id)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
	 	->select('DISTINCT a.*');

		$query->from('`#__vendor_table` AS a');

    	$query->where('a.state = 1');

		$query->where('a.id = '.$db->quote($vendor_id));

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}
	private static function findUpdateApk($app)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
	 	->select('DISTINCT a.*');

		$query->from('`#__apkupdate_table` AS a');

    	$query->where('a.state = 1');

		$query->where('a.app = '.$db->quote($app));

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}

	public static function getVendorData()
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
	 	->select('DISTINCT a.*');

		$query->from('`#__vendor_table` AS a');

    	$query->where('a.state = 1');

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
	}
	public static function MyUpdate()
	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->updatename = $app->input->getVar('updatename',"");
		$items = self::findUpdateApk($obj->updatename);

		$obj->ver = '0';

		if(count($items))
		{
			$obj->ver = $items[0]->version;
			$obj->path = $items[0]->path;

		}
		return json_encode($obj);


	}

	public static function reload_ipcam($ip)
	{
		$obj = new stdClass;
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.getBaIPCamItems",

			);

		$ips = self::getBaList($ip);

		$obj->arr = $arr;

		$obj->ips = $ips;
		$timeout = 20;
		$json = self::sendToUrl1($obj,$timeout);

		$myfile = fopen("/tmp/backup.sql", "w") or die("Unable to open file!");

		fwrite($myfile, $json);

		fclose($myfile);

		system("mysql -u joomlauser --password=123456  joomladb < /tmp/backup.sql");

		return $json;
	}
	private static function findMailParent($id)
	{
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
	  $query
	   ->select('DISTINCT a.*');

	  $query->from('`#__ipcam_table` AS a');

	  $query->where('a.state = 1');

	  $query->where('a.enable = 1');

	  $query->where('a.parent = 0');

	  $query->where('a.id = '.$db->quote($id));

	  $db->setQuery($query);

	  $items = (array) $db->loadObjectList();

	  return $items;
	}
	private static function findMailList($id)
	{
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
	  $query
	   ->select('DISTINCT a.*');

	  $query->from('`#__ipcam_table` AS a');

	  $query->where('a.state = 1');

	  $query->where('a.enable = 1');

	  $query->where('a.parent = '.$db->quote($id));

	  $query->where('a.note1 = '.self::$mail_note1);

	  $query->order($db->escape('a.id DESC'));

	  $db->setQuery($query);

	  $items = (array) $db->loadObjectList();

	  return $items;
	}
	private static function findAccIPByNumber($number)
	{
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
	  $query
	   ->select('DISTINCT a.*');

	  $query->from('`#__ipcam_table` AS a');

	  $query->where('a.state = 1');

	  $query->where('a.enable = 1');

	  $query->where('a.parent <> 0');

	  $query->where('a.number = '.$db->quote($number));

	  $query->where('a.note1 = '.self::$app6_note1);

	  $db->setQuery($query);

	  $items = (array) $db->loadObjectList();

	  return $items;
	}
	private static function findAccIP($obj)
	{
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
	  $query
	   ->select('DISTINCT a.*');

	  $query->from('`#__ipcam_table` AS a');

	  $query->where('a.state = 1');

	  $query->where('a.enable = 1');

	  $query->where('a.parent <> 0');

	  $query->where('a.number = '.$db->quote($obj->number));

	  $query->where('a.note1 = '.self::$app6_note1);

	  $db->setQuery($query);

	  $items = (array) $db->loadObjectList();

	  return $items;
	}
  	private static function findAccLogin($obj)
  	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
	 	->select('DISTINCT a.*');

		$query->from('`#__ipcam_table` AS a');

    	$query->where('a.state = 1');

    	$query->where('a.enable = 1');

		$query->where('a.parent <> 0');

		$query->where('a.user = '.$db->quote($obj->user));

		$query->where('a.passwd = '.$db->quote($obj->passwd));

		$query->where('a.number = '.$db->quote($obj->number));

    	$query->where('a.note1 = '.self::$app6_note1);

		$db->setQuery($query);

		$items = (array) $db->loadObjectList();

		return $items;
  	}
	  private static function write_status($value)
	  {
		  $myfile = fopen("/tmp/newfile2.txt", "w") or die("Unable to open file!");
		  $txt = $value."\n";

		  fwrite($myfile, $txt);

		  fclose($myfile);
	  }

	private static function write_unlock($value)
	{
		$myfile = fopen("/tmp/newfile1.txt", "w") or die("Unable to write file1!");
		$txt = $value."\n";

		fwrite($myfile, $txt);

		fclose($myfile);
	}
	private static function write_gas_status($value)
	{
		$myfile = fopen("/tmp/newfile3.txt", "w") or die("Unable to write file3!");
		$txt = $value."\n";

		fwrite($myfile, $txt);

		fclose($myfile);
	}

	private static function getNumberTable($number)
	{
		$items = self::getMyAccount();
		$elec_alarm = 0;
		if(count($items))
		{
			$elec_alarm = $items[0]->elec_alarm;
		}

		$str = $elec_alarm.",";
		$items = self::getElecItem($number);
		if(count($items))
		{
			//$str = json_encode($items[0]);
		}

		if(count($items))
		{
			$str = $str.$items[0]->elec_v.",";
			$str = $str.$items[0]->elec_v_bn.",";
			$str = $str.$items[0]->elec_v_cn.",";
			$str = $str.$items[0]->elec_a_a.",";
			$str = $str.$items[0]->elec_a_b.",";
			$str = $str.$items[0]->elec_a_c.",";
			$str = $str.$items[0]->elec_pf.",";
			$str = $str.$items[0]->elec_kw.",";
			$str = $str.$items[0]->elec_kvar.",";
			$str = $str.$items[0]->elec_kwh.",";

		}

		$str = $str."3.13";
		return $str;
	}
	private static function getTable()
	{
		$app  = JFactory::getApplication();

		$obj->number = $app->input->getVar('number',"");

		return $str = self::getNumberTable($obj->number);

	}

	private static function getWater()
	{
		$obj = new stdClass;

		$app  = JFactory::getApplication();

		$number = $app->input->getInt('number',"");
		$type = $app->input->getInt('type',"1");
		/*
		$items = self::getElecItem($number);
		if(count($items))
			$id = $items[0]->id;
		*/
		$d = date('Y-m-d');
		$day = $app->input->getVar('date',$d);

		$obj->id = 710;
		$obj->day = $day;
		$obj->year = strtok($day, "-");
		$obj->month = strtok("-");

		if($type == 1)
			return self::getWaterDay1($obj);
		else if($type == 2)
			return self::getWaterMonth1($obj);
		else
			return self::getWaterYear1($obj);
	}
	private static function getWaterDay1($obj)
	{
		$arr = array();

		for($i=0;$i<24;$i++)
		{
			$water = new stdClass;
			$water->index = $i;
			$water->mt = 0;
			$water->acc_mt = 17.05;
			array_push($arr,$water);
		}

		$str = json_encode($arr);
		return $str;
	}
	private static function getWaterMonth1($obj)
	{
		$arr = array();
		$date_arr=array(
			　1=>31,
			　2=>28,
			　3=>31,
			  4=>30,
			　5=>31,
			　6=>30,
			  7=>31,
			  8=>31,
			  9=>30,
			  10=>31,
			  11=>30,
			  12=>31,

		  );

		//echo(" 123 ".$month." ".$date_arr[$month]." 456");
		$month = intval($obj->month);
		foreach (range(1, $date_arr[$month]) as $n)
		{
			$water = new stdClass;
			$num_padded = sprintf("%02d / %02d", $obj->month,$n);
			$water->index = $num_padded;
			$water->mt = 0;
			$water->acc_mt = 17.05;
			array_push($arr,$water);
		}

		$str = json_encode($arr);
		return $str;
	}
	public static function getWaterYear1($obj)
	{
		$arr = array();

		foreach (range(1, 12) as $n)
		{
			$water = new stdClass;
			$water->index = $n;
			$water->mt = 0;
			$water->acc_mt = 17.05;
			array_push($arr,$water);
		}

		$str = json_encode($arr);
		return $str;

	}
	private static function process_cmd($obj)
	{
		$myfile = fopen("/tmp/newfile1.txt", "r") or die("Unable to open file1!");
		$status = fgets($myfile);
		//$gas_status = fgets($myfile);
		fclose($myfile);

		$myfile = fopen("/tmp/newfile3.txt", "r") or die("Unable to open file3!");
		$gas_status = fgets($myfile);
		//$gas_status = fgets($myfile);
		fclose($myfile);

		$status = strtok($status,"\n");
		$gas_status = strtok($gas_status,"\n");
		if($obj->cmd == "APPGETSTATE")
		{
			$str = "STATE1,0,".$status.",0,0,".$gas_status;
			self::write_status($str);
			//$str = "STATE,0,0,0,0";
		}
		else if($obj->cmd == "LOCK")
		{
			$str = "LOCKOK";
			self::write_unlock("1");
		}
		else if($obj->cmd == "SETGAS 1")
		{
			$gas_status = "1";

			self::write_gas_status($gas_status);
			$str = "STATE1,0,".$status.",0,0,".$gas_status;
			self::write_status($str);

			$str = "SETGASOK";
		}
		else if($obj->cmd == "SETGAS 0")
		{
			$gas_status = "0";
			self::write_gas_status($gas_status);
			$str = "STATE1,0,".$status.",0,0,".$gas_status;
			self::write_status($str);

			$str = "SETGASOK";
		}

		else
		{
			$ret = "UNLOCKFAIL";
			$str = strtok($obj->cmd," ");
			if($str == "UNLOCK")
			{
				$str = strtok(" ");
				if($str == "0001")
				{
					$ret = "UNLOCKOK";
					self::write_unlock("0");
				}

			}

			$str = $ret;
		}

		return $str;
	}
  	private static function doGetFromPhone($obj)
  	{
		//String str = "STATE,"+str_dnd+","+str_lock+","+str_alarm_delay_time+","+myApp.get_sos_output();

		if($obj->cmd == "GETTABLE")
		{
			return self::getTable();

		}
		else if($obj->cmd == "GETELEC")
		{
			//return self::getElec();
			return self::getMobileDay();

		}
		else if($obj->cmd == "GETWATER")
		{
			return self::getWater();

		}
		//return self::process_cmd($obj);

		$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
		if(!is_resource($socket))
		{
			onSocketFailure("Failed to create socket");
			return false;
		}

		//JLog::add($obj->cmd, JLog::INFO, 'jerror');

		$ret = socket_connect($socket, $obj->ipaddr, self::$SERVER_PORT);
		if($ret == false)    return false;

		//or onSocketFailure("Failed to connect to chat.stackoverflow.com:6667", $socket);
		socket_write($socket, $obj->cmd."\n");

		$str = '';
		while(true) {
			// read a line from the socket
			$line = socket_read($socket, 1024, PHP_NORMAL_READ);
			if(substr($line, -1) === "\n") {
				// read/skip one byte from the socket
				// we assume that the next byte in the stream must be a \n.
				// this is actually bad in practice; the script is vulnerable to unexpected values
				$str = $line;
				break;
			}

		}

		JLog::add($str, JLog::INFO, 'jerror');
		socket_close($socket);

		return $str;
  	}
	public static function getFromPhone()
  	{
		$app  = JFactory::getApplication();

		$obj = new stdClass;

		$obj->ipaddr = $app->input->getVar('ipaddr',"");
		$obj->cmd = $app->input->getVar('cmd',"");

		$ret = self::doGetFromPhone($obj);

		return $ret;
  	}
	private static function sendMailContent($obj)
	{
		$items = self::findMailList($obj->id);

		if(count($items) == 0)    return false;

		$str = '$';
		foreach($items as $i=>$item)
		{
			$str = $str.$item->user;
			if(($i%5) == 4)
			{

				$obj->cmd = "CONFIG,SETMAIL,".$str;

				$ret = self::doGetFromPhone($obj);
				if($ret == false)    return false;

				$ret_str = substr($ret,0,11);
				if($ret_str != "MAILSEND,OK") return false;
				$str = '';
			}
			else if($i < (count($items)-1))
			{
				$str = $str."&&";
			}

		}

		if($str != '')
		{
			$obj->cmd = "CONFIG,SETMAIL,".$str;
			$ret = self::doGetFromPhone($obj);
			if($ret == false)    return false;
			$ret_str = substr($ret,0,11);
			if($ret_str != "MAILSEND,OK") return false;

		}

		return true;

	}
	public static function sendMailToPhone($id)
  	{
		$obj = new stdClass;

		$items = self::findMailParent($id);
		if(count($items) == 0)    return false;

		$numbers = explode(',',$items[0]->number);
		foreach($numbers as $i=>$number)
		{
			//JLog::add($number, JLog::INFO, 'jerror');

			$ips = self::findAccIPByNumber($number);
			if(count($ips) == 0)    return false;

			//JLog::add($ips[0]->ip, JLog::INFO, 'jerror');
			$obj->id = $items[0]->id;
			$obj->ipaddr = $ips[0]->ip;

			$ret = self::sendMailContent($obj);

			if($ret == false)    return false;
		}

		return $ret;
  	}

	public static function sendBCToPhone($obj)
  	{
		if($obj->enable == 0)    return;

		$arr = array(
			"id" => "0",
			"list"   => $obj->number,
			"type"   => "2",
			"text"   => $obj->user,

			  );

		$ips = array("***********");

		$obj->arr = $arr;

		$obj->ips = $ips;
		$obj->path = "/api/users/setbc";
		$json = self::sendToUrl2($obj);

		//JLog::add($json,JLog::INFO, 'jerror');

		return $ret;
  	}
  public static function myLogin()
  {
	$app  = JFactory::getApplication();

	$obj = new stdClass;

	$obj->user = $app->input->getVar('NAME',"");
	$obj->passwd = $app->input->getVar('PASSWD',"");
	$obj->number = $app->input->getVar('NUMBER',"");

	$items = self::findAccLogin($obj);

	if(count($items))    return $items[0]->ip;
	return false;
  }

  private static function do_send_gas($obj)
  {
	if($obj->number == "")    return false;
	if($obj->value == "")    return false;
	if($obj->year == "")    return false;
	if($obj->month == "")    return false;
	if($obj->day == "")    return false;

	$str = $obj->number." ";
	$str = $str.$obj->value." ";
	$str = $str.$obj->year." ";
	$str = $str.$obj->month." ";
	$str = $str.$obj->day;

	system("echo ".$str." >> /opt/gas.txt");

	$accs = self::getMyAccount();

	$acc = $accs[0];

	if($acc->ftp_enable == 0)    return true;

	$host = $acc->ftp_server;
	$port = $acc->ftp_port;
	$user = $acc->ftp_user;
	$pwd = $acc->ftp_passwd;

	$cmd = 'curl -T /opt/gas.txt ftp://'.$user.':'.$pwd.'@'.$host.'/gas.txt';

	return true;

  }
  public static function send_gas()
  {
	$app  = JFactory::getApplication();

	$obj = new stdClass;

	$obj->number = $app->input->getVar('number',"");
	$obj->value = $app->input->getVar('value',"");
	$obj->year = $app->input->getVar('year',"");
	$obj->month = $app->input->getVar('month',"");
	$obj->day = $app->input->getVar('day',"");

	$ret = self::do_send_gas($obj);

	if($ret)
	{
		return "OK";
	}
	return "ERROR";
  }
  public static function getSOSItems()
  {
	$obj = new stdClass;
	$arr = array(
		"option" => "com_floor",
		"task"   => "sroots.getBaSOSItems",

		);

	$ips = self::getBaList();

	$obj->arr = $arr;

	$obj->ips = $ips;

	$timeout = 20;
	$json = self::sendToUrl1($obj,$timeout);

	$myfile = fopen("/tmp/backup.sql", "w") or die("Unable to open file!");

	fwrite($myfile, $json);

	fclose($myfile);

	system("mysql -u joomlauser --password=123456  joomladb < /tmp/backup.sql");

	return $json;

  }
  private static function do_sendBcMessage($obj)
  {

    $callee = '';

	foreach($obj->arr as $i=>$item)
	{
	    $callee = $item.'-'.$callee;

	}

	//socket_create
	$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

	if ($socket < 0) {
		//echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
	    //echo("\n");
		return;
	}

	//socket_connect
	$result = socket_connect($socket, "/tmp/phpsocket");

	if ($result < 0) {
		//echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

	    //echo("\n");
		//Close
		socket_close($socket);
		return;
	}

	$desc = "bc=bc&".$obj->type."&".$obj->text."&";

	$in = $desc.'=callee='.$callee;//input, transfer msg to server side

	//socket_write
	if(!socket_write($socket, $in, strlen($in))) {
		//echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
	   // echo("\n");
	}

 	//Close
	socket_close($socket);

	return $desc." ".$in;

  }
  public static function getSIPDevices()
  {
	// Create a new query object.
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	$query->from('`#__device_table` AS a');

    $query->where('a.state = 1');

    $query->where('a.type = 1');

    $query->where('a.enable = 1');

	$db->setQuery($query);

	$items = (array) $db->loadObjectList();

	return $items;

  }


  public static function getPhoneItems($id)
  {
	  $bc_items = self::getBcItemById($id);

	  $arr = array();

	  if(count($bc_items) == 0)    return $arr;

	  $bc_range = $bc_items[0]->range;

	  $str_sec = explode(",",$bc_range);

	  $items = self::getSIPDevices();

	  foreach($str_sec as $i1=>$item1)
	  {
		$value = explode("-",$item1);
		$cnt = count($value);

		if($cnt == 0)    continue;

		if($cnt == 1)
		{
			array_push($arr,$item1);
			continue;
		}

		$start = intval($value[0]);
		$end = intval($value[1]);

	    foreach($items as $i=>$item)
	    {
			if($item->info == "")    continue;

			$time = intval($item->info);
            if($time >= $start && $time <= $end)
		        array_push($arr,$item->info);
	    }
	  }
	  return $arr;
  }
  public static function Broadcast($obj)
  {
	//return "1234";
	if($obj->id == 0)
	{
		$obj->arr = explode(",",$obj->list);
		$ret = self::do_sendBcMessage($obj);
		return $ret;
	}
	$arr = array(
	"option" => "com_floor",
	"task"   => "sroots.getPhoneItems",
	"id"   => $obj->id,

	  );

	$ips = self::getBaList();

	$obj->arr = $arr;

	$obj->ips = $ips;

	$json = self::sendToUrl1($obj);

	$items = json_decode($json,true);

	$arr = array();
	foreach($items as $i=>$item)
	{
		array_push($arr,$item);

		if(count($arr) >= 50)
		{
			$obj->arr = $arr;
			self::do_sendBcMessage($obj);
			$arr = array();
		}
	}

	if(count($arr) > 0)
	{
		$obj->arr = $arr;
		self::do_sendBcMessage($obj);
	}
	return $json;

  }
  public static function getBaBcItem($name)
  {
	//return "1234";
	$arr = array(
	"option" => "com_floor",
	"task"   => "sroots.getBcItem",
	"name"   => $name,

	  );

	$ips = self::getBaList();

    $obj = new stdClass;

	$obj->arr = $arr;

	$obj->ips = $ips;

	return self::sendToUrl1($obj);

  }

  public static function getBaBcItems()
  {
	//return "1234";
	$arr = array(
	"option" => "com_floor",
	"task"   => "sroots.getBcItems",

	  );

	$ips = self::getBaList();

    $obj = new stdClass;

	$obj->arr = $arr;

	$obj->ips = $ips;

	return self::sendToUrl1($obj);

  }

  public static function getBaLimitcallItem($range)
  {
	//return "1234";
	$arr = array(
	"option" => "com_floor",
	"task"   => "sroots.getLimitcallItem",
	"range"   => $range,

	  );

	$ips = self::getBaList();

    $obj = new stdClass;

	$obj->arr = $arr;

	$obj->ips = $ips;

	return self::sendToUrl1($obj);

  }
  private static function sendToUrl1($obj,$timeout=5)
  {
	$ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($obj->arr));
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

	$output = '{}';
	foreach($obj->ips as $i=>$ip)
	{
		$url = "http://".$ip->url.'/index.php';

		curl_setopt($ch, CURLOPT_URL, $url);

		$result = curl_exec($ch);

		if($result === FALSE) {
		}
		else
		{
			$output = $result;
		    break;
		}

	}

	curl_close($ch);

	return $output;

  }
  private static function sendToUrl2($obj,$timeout=5)
  {
	$ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($obj->arr));
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

	$output = '{}';
	foreach($obj->ips as $i=>$ip)
	{
		$url = "http://".$ip.$obj->path;

		//JLog::add($url,JLog::INFO, 'jerror');
		curl_setopt($ch, CURLOPT_URL, $url);

		$result = curl_exec($ch);

		if($result === FALSE) {

		}
		else
		{
			$output = $result;
		    break;
		}

	}

	curl_close($ch);

	return $output;

  }
  public static function string_xto($digit,$str)
  {
	$new_str = str_replace("x",$digit,$str);
	$new_str = str_replace("X",$digit,$new_str);

	return $new_str;
  }
  public static function check_range($name,$item)
  {
	//echo($item->range);
	$str_sec = explode(",",$item->range);

	$int_name = intval($name);
	foreach($str_sec as $i=>$str_range)
	{
		//echo($str_range);
		$str_range_arr = explode("-",$str_range);

		if(sizeof($str_range_arr) == 2)
		{
			$str_low = self::string_xto("0",$str_range_arr[0]);
			$str_high = self::string_xto("9",$str_range_arr[1]);
			//echo("value ".$str_low." ".$str_high." ".$name);
			$int_low = intval($str_low);
			$int_high = intval($str_high);

			if($int_name >= $int_low && $int_name <= $int_high)
		    {
				return true;
			}
		}
		else
		{
			//echo("continue");
			continue;
		}
	}

	return false;
  }
  public static function getBcItems()
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__limitcall_table` AS a');

    $query->where('a.state = 1');
    $query->where('a.type = '.self::$bc_type );

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;

  }

  public static function findSOSNumber($number)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__ipcam_table` AS a');

	$query->where('a.state = 1');
	$query->where('a.enable = 1');
	$query->where('a.number = '.$db->quote($number));
	$query->where('a.note1 = '.self::$sos_note1);
    $query->where('a.parent <> 0' );

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;
  }
  public static function findAlarmMsg($msg)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__device_table` AS a');

	$query->where('a.state = 1');
	$query->where('a.enable = 1');
    $query->where('a.info = '.$db->quote($msg));

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;
  }
  public static function getIPCamItems()
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__ipcam_table` AS a');

	$query->where('a.state = 1');
	$query->where('a.note1 = '.self::$ip_note1);
    $query->where('a.parent <> 0' );

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;

  }
  public static function getBcItemById($id)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__limitcall_table` AS a');

    $query->where('a.state = 1');
    $query->where('a.id = '.$db->quote($id) );

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;

  }

  public static function getBcItem($name)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__limitcall_table` AS a');

    $query->where('a.state = 1');
    $query->where('a.type = '.self::$bc_type );
    $query->where('a.name = '.$db->quote($name) );

    $db->setQuery($query);
    //echo($query);
    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

	return $items;

  }
  public static function getLimitcallItem($name)
  {
	$items = self::get_limit_top(0);

	foreach($items as $i=>$item)
	{
		if($item->enable == 0)    continue;

		if(self::check_range($name,$item) == true)
		{
			return $item;
		}
	}

    $obj = new stdClass;
	return $obj;

  }
  public static function add_new_bc_item($obj)
  {
	$db = JFactory::getDbo();
	$db->setQuery('SELECT MAX(ordering) FROM #__limitcall_table');
	$max = $db->loadResult();
	$ordering = $max+1;

	$created_by = JFactory::getUser()->name;
	$checked_out = false;
	$state = 1;

	// Create a new query object.
	$query = $db->getQuery(true);

	// Insert columns.
	$columns = array(
	  'ordering',
	  'state',
	  'checked_out',
	  'created_by',
	  'type',
	  'parent',
	  'name',
	  'enable',

	  'range',


	);

	// Insert values.
	$values = array(
	  $db->quote($ordering),
	  $db->quote($state),
	  $db->quote($checked_out),
	  $db->quote($created_by),
	  $db->quote($obj->type),
	  $db->quote($obj->myid),
	  $db->quote($obj->name),
      $db->quote($obj->enable),

	  $db->quote($obj->range),

	);

	// Prepare the insert query.
	$query
		->insert($db->quoteName('#__limitcall_table'))
		->columns($db->quoteName($columns))
		->values(implode(',', $values));

	// Set the query using our newly populated query object and execute it.
	$db->setQuery($query);
	$db->execute();

	//JLog::add($query, JLog::INFO, 'jerror');
	$db->setQuery('SELECT MAX(id) FROM #__limitcall_table');
	$max = $db->loadResult();

	$obj->myid = $max;

  }
  public static function add_new_limit_item($obj)
  {
	$db = JFactory::getDbo();
	$db->setQuery('SELECT MAX(ordering) FROM #__limitcall_table');
	$max = $db->loadResult();
	$ordering = $max+1;

	$created_by = JFactory::getUser()->name;
	$checked_out = false;
	$state = 1;

	// Create a new query object.
	$query = $db->getQuery(true);

	// Insert columns.
	$columns = array(
	  'ordering',
	  'state',
	  'checked_out',
	  'created_by',
	  'type',
	  'parent',
	  'name',
	  'enable',
	  'night_hour',
	  'night_min',
	  'night_hour_end',
	  'night_min_end',
	  'night_call',
	  'night_enable',
	  'range',
	  'allow',

	);

	// Insert values.
	$values = array(
	  $db->quote($ordering),
	  $db->quote($state),
	  $db->quote($checked_out),
	  $db->quote($created_by),
	  $db->quote($obj->type),
	  $db->quote($obj->myid1),
	  $db->quote($obj->name),
      $db->quote($obj->enable),
	  $db->quote($obj->night_hour),
      $db->quote($obj->night_min),
	  $db->quote($obj->night_hour_end),
      $db->quote($obj->night_min_end),
      $db->quote($obj->night_call),
	  $db->quote($obj->night_enable),
	  $db->quote($obj->range),
	  $db->quote($obj->allow),
	);

	// Prepare the insert query.
	$query
		->insert($db->quoteName('#__limitcall_table'))
		->columns($db->quoteName($columns))
		->values(implode(',', $values));

	// Set the query using our newly populated query object and execute it.
	$db->setQuery($query);
	$db->execute();

	//JLog::add($query, JLog::INFO, 'jerror');
	$db->setQuery('SELECT MAX(id) FROM #__limitcall_table');
	$max = $db->loadResult();

	$obj->myid = $max;

  }
  public static function get_bc_top($top_id)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__limitcall_table` AS a');

    $query->where('a.state = 1');
    $query->where('a.type = '.self::$bc_type );

    if($top_id > 0)
	   $query->where($db->quoteName('a.parent') . ' = ' . $db->quote($top_id));

    $db->setQuery($query);

    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

    return $items;
  }

  public static function get_limit_top($top_id)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
        ->select('DISTINCT a.*');

    $query->from('`#__limitcall_table` AS a');

    $query->where('a.state = 1');
    $query->where('a.type = '.self::$limit_type );

    if($top_id > 0)
	   $query->where($db->quoteName('a.parent') . ' = ' . $db->quote($top_id));

    $db->setQuery($query);

    //JLog::add($query, JLog::INFO, 'jerror');
    $items = (array) $db->loadObjectList();

    return $items;
  }


  public static function update_top_enable($table,$obj)
  {
	  $db = JFactory::getDbo();

	  $fields = array(

		  $db->quoteName('enable') . ' = ' . $db->quote($obj->enable),

	  );

	  $conditions = array(
			  $db->quoteName('id') . ' = ' . $db->quote($obj->id),

	  );
	  $query = $db->getQuery(true);
	  $query->update($db->quoteName($table))->set($fields)->where($conditions);

	  $db->setQuery($query)->execute();

  }

	public static function send_reload_center($str='0')
	{
		$ips = self::getCenterList();

		foreach($ips as $i=>$ip)
		{
	    	$cmd = '/index.php?option="com_floor&task=sroots.reload&str='.$str."\" -k --connect-timeout 3 -o /tmp/RELOAD  > /dev/null 2>&1 &";
        	$cmd = 'curl https://'.$ip->url.$cmd;
			system($cmd);
	    	//echo($cmd);
  		}
	}

  public static function dir_save_item()
  {
	$app = JFactory::getApplication();

	$cid = $app->input->get('device_enable', array(), 'array');
	$name_id = $app->input->get('name_id', array(), 'array');

	$obj = new stdClass;

	foreach($name_id as $i=>$item)
	{
 	    //JLog::add($item, JLog::WARNING, 'jerror');
		$obj->id = $item;
		$obj->enable = $cid[$i];

		self::update_top_enable('#__limitcall_table',$obj);

	}

	self::send_reload_center();

	JLog::add(JText::_('COM_LIMITCALL_CHANGE_OK'), JLog::WARNING, 'jerror');


  }

  public static function getGroupItem($table,$mygroup)
  {
	// Create a new query object.
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
	$query
		 ->select('DISTINCT a.*');

	$query->from('`'.$table.'` AS a');

	$query->where('a.state = 1');

    if($mygroup > 0)
	   $query->where('a.id = '.$mygroup);

	$db->setQuery($query);

	$items = (array) $db->loadObjectList();

	return $items;

  }

  public static function find_subitem($table,$item)
  {
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
   $query
   ->select('DISTINCT a.*');

   $query->from('`'.$table.'` AS a');

   $query->where('a.state = 1');

   $query->where('a.parent = '.$item);

   $db->setQuery($query);

   $items = (array) $db->loadObjectList();

   return $items;

  }


  public static function getElecId($name)
  {
	$ips = self::getBaList();
	$param = '/index.php?option="com_floor&task=sroots.getElecItem&name='.$name.'"  -k --connect-timeout 3 -o /tmp/GETELEC > /dev/null 2> /dev/null';
	foreach($ips as $i=>$ip)
	{
		$cmd = 'curl https://'.$ip->url.$param;
		system($cmd);
		break;
	}

	$myfile = fopen("/tmp/GETELEC", "r") or die("Unable to open file!");
	$json = fread($myfile,filesize("/tmp/GETELEC"));
	fclose($myfile);

	$id = json_decode($json,true);
	return $id;
  }


  public static function getElecItem($name)
  {
	  //return $name;
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  $query
		 ->select('DISTINCT a.*');

	  $query->from('`#__device_table` AS a');

	  $query->where('a.state = 1');
      $query->where('a.note LIKE "'.$name.'"');
	//   $query->where('a.dio_type = '.self::$rs485_elec_device);
	//   $query->where('a.dio_type'.' IN '.$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic)));
	  $query->where('a.dio_type IN ('.implode(',',$db->quote(
		array(
			self::$rs485_elec_device,
			self::$rs485_elec_device_cic,
			self::$rs485_elec_device_daepm210,
			self::$rs485_elec_device_acuvim,
			self::$rs485_elec_device_tatung,
			self::$rs485_vmr_mp7_elec_device,
			self::$rs485_vmr_mp8_elec_device))).")");
	  //JLog::add($query, JLog::INFO, 'jerror');
	  $db->setQuery($query);
	   //echo($query);
	   //JLog::add($query, JLog::INFO, 'jerror');

	  $items = (array) $db->loadObjectList();

	  return $items;

  }
  public static function getThisTypeData($obj)
  {
	$db = JFactory::getDbo();
	$query = $db->getQuery(true);

	// Select the required fields from the table.
    $query
     ->select('DISTINCT a.*');

    $query->from('`#__elec_table` AS a');

    $query->where('a.state = 1');

    if($obj->type > 0)
	   $query->where('a.type = '.$obj->type);

    if($obj->timestamp > 0)
    {
	   $query->where('a.timestamp >= '.(int)$obj->timestamp);
    }

    $query->where('a.dev_id = '.(int)$obj->id);

    $db->setQuery($query);

    $items = (array) $db->loadObjectList();
    return $items;
  }

  public static function thisDayTimestamp($year,$month,$day)
  {
	  $d = DateTime::createFromFormat(
		  'Y-m-d H:i:s',
		  $year.'-'.$month.'-'.$day.' 00:00:00',
		  new DateTimeZone('Asia/Taipei')
	  );

	  if ($d === false) {
		  die("Incorrect date string");
	  } else {
		  //echo $d->getTimestamp();
	  }
		  return $d->getTimestamp();
  }

  public static function thisMonthTimestamp($year,$month)
  {
	  $d = DateTime::createFromFormat(
		  'Y-m-d H:i:s',
		  $year.'-'.$month.'-01'.' 00:00:00',
		  new DateTimeZone('Asia/Taipei')
	  );

	  if ($d === false) {
		  die("Incorrect date string");
	  } else {
		  //echo $d->getTimestamp();
	  }
		  return $d->getTimestamp();
  }

  public static function getYear()
  {
	$app  = JFactory::getApplication();

	$obj = new stdClass;

	$obj->id = $app->input->getInt('id',710);

	$day = date('Y-m-d');

	$obj->year = strtok($day, "-");
	$obj->month = strtok("-");

    return self::getYear1($obj);
  }
  public static function getYear1($obj)
  {
	$Echarge = array();

	$date = array();

	$fees = array();
	$kwhs = array();

	$obj->timestamp = 0;
    $obj->type = self::$month_type;
    $items = self::getThisTypeData($obj);
    //print_r($items);
	foreach (range(1, 12) as $n)
	{
	   $timestamp = self::thisMonthTimestamp($obj->year,$n);

	   $fee = '0';
	   $kwh = '0';
	   foreach($items as $i=>$item)
	   {
			//echo(" abc ".$item->imestamp . " ".$timestamp." abc");
			if(intval($item->timestamp) == intval($timestamp))
			{
			    //echo("equ");
			    $fee = $item->elec_kwh_cur;
			    $kwh = $item->elec_kwh;

			    break;
			}

			//$Echarge[$iem->timstamp] = $iem->fee;
		}


	    $myfee = array($timestamp*1000,$fee);

	    $myfee1 = array($myfee);
	    $Echarge = array_merge($Echarge,$myfee1);
		$cur_date = array($n);
		$cur_fee = array($fee);
		$cur_kwh = array($kwh);

		$date = array_merge($date,$cur_date);
		$fees = array_merge($fees,$cur_fee);
		$kwhs = array_merge($fees,$cur_kwh);

	}

	$obj->Interval = 'year';
	$obj->data = $Echarge;

	$obj->date = $date;
	$obj->fees = $fees;
	$obj->kwhs = $kwhs;

	return $json = json_encode($obj);

  }


  public static function getMonth()
  {
	$app  = JFactory::getApplication();

	$id = $app->input->getInt('id',710);

	$obj = new stdClass;

	$day = date('Y-m-d');

	$obj->id = $id;

	$obj->year = strtok($day, "-");
	$obj->month = strtok("-");

	return self::getMonth1($obj);
  }
  public static function getMonth1($obj)
  {
	self::update_elec_all();

	$Echarge = array();

	$date = array();

	$fees = array();
	$kwhs = array();

    $obj->timestamp = self::thisDayTimestamp($obj->year,$obj->month,1);

    $obj->type = self::$day_type;
    $items = self::getThisTypeData($obj);

	$date_arr=array(
	  　1=>31,
	  　2=>28,
	  　3=>31,
		4=>30,
	  　5=>31,
	  　6=>30,
		7=>31,
	    8=>31,
	    9=>30,
	    10=>31,
	    11=>30,
	    12=>31,

	);

	$date1 = strtotime($obj->year."-02-01 00:00:00");
	$date2 = strtotime($obj->year."-03-01 00:00:00");

	// Formulate the Difference between two dates
	$diff = abs($date2 - $date1);
    $diff = $diff/86400;
	$date_arr[2] = $diff;
	//echo(" 123 ".$month." ".$date_arr[$month]." 456");
	$month = intval($obj->month);
	foreach (range(1, $date_arr[$month]) as $n)
	{
	    $timestamp = self::thisDayTimestamp($obj->year,$obj->month,$n);

	    $fee = '0';
		$kwh = '0';
		foreach($items as $i=>$item)
		{
			//echo(" ".$item->imestamp . " ".$timestamp." ");
			if(intval($item->timestamp) == intval($timestamp))
		    {
		        //echo("equ");
			    $fee = $item->elec_kwh_cur;
				$kwh = $item->elec_kwh;

				break;
			}
				 //$Echarge[$iem->timstamp] = $iem->fee;
		  }
		  //$timestamp = $timestamp-57600;
	      $myfee = array($timestamp*1000,$fee);

		  $myfee1 = array($myfee);

	      $Echarge = array_merge($Echarge,$myfee1);
		  $num_padded = sprintf("%02d / %02d", $obj->month,$n);
		  $cur_date = array($num_padded);
		  $cur_fee = array($fee);
		  $cur_kwh = array($kwh);

		  $date = array_merge($date,$cur_date);
		  $fees = array_merge($fees,$cur_fee);
		  $kwhs = array_merge($kwhs,$cur_kwh);
	}

	$obj->Interval = 'month';
	$obj->data = $Echarge;

	$obj->date = $date;
	$obj->fees = $fees;
	$obj->kwhs = $kwhs;
	$json = json_encode($obj);
	return $json;
  }


  private static function getMobileDay()
  {
	$obj = new stdClass;

	$app  = JFactory::getApplication();

	$number = $app->input->getInt('number',"");
	$type = $app->input->getInt('type',"1");

	$items = self::getElecItem($number);
	if(count($items))
		$id = $items[0]->id;
	$d = date('Y-m-d');
    $day = $app->input->getVar('date',$d);

	$obj->id = $id;
	$obj->day = $day;
	$obj->year = strtok($day, "-");
	$obj->month = strtok("-");

	if($type == 1)
		return self::getDay1($obj);
	else if($type == 2)
		return self::getMonth1($obj);
	else
		return self::getYear1($obj);
  }
  public static function getDay()
  {
	$obj = new stdClass;

	$app  = JFactory::getApplication();

	$id = $app->input->getInt('id',710);

	$d = date('Y-m-d');
    $day = $app->input->getVar('mydate',$d);

	$obj->id = $id;
	$obj->day = $day;
	$obj->year = strtok($day, "-");
	$obj->month = strtok("-");

	return self::getDay1($obj);
  }
  public static function getDay1($obj)
  {

    $Echarge = array();

    $date = array();

	$fees = array();
	$kwhs = array();

    $obj->timestamp = self::thisHourTimestamp(0,$obj->day);

    $obj->type = self::$hour_type;

    $items = self::getThisTypeData($obj);
    //print_r($items);

	//echo(' count '.count($items).' '.$type.' ');

	foreach (range(0, 23) as $n)
	{
	    $timestamp = self::thisHourTimestamp($n,$obj->day);

	    $fee = '0';
		$kwh = '0';


		foreach($items as $i=>$item)
		{
		  //echo(" ".$item->imestamp . " ".$timestamp." ");
			if(intval($item->timestamp) == intval($timestamp))
		    {
			  //echo("equ");
			  $fee = $item->elec_kwh_cur;
			  $kwh = $item->elec_kwh;

			  break;
		    }
		    //$Echarge[$iem->timstamp] = $iem->fee;
		}

		$timestamp = $timestamp-57600;
		$myfee = array($timestamp*1000,$fee);
		$mykwh = array($timestamp*1000,$kwh);

	    $cur_date = array($n);
	    $cur_fee = array($fee);
		$cur_kwh= array($kwh);

		$myfee1 = array($myfee);

	    $Echarge = array_merge($Echarge,$myfee1);
	    $date = array_merge($date,$cur_date);
		$fees = array_merge($fees,$cur_fee);
		$kwhs = array_merge($kwhs,$cur_kwh);

	}

	$obj->Interval = 'day';
	$obj->data = $Echarge;

	$obj->date = $date;
	$obj->fees = $fees;
	$obj->kwhs = $kwhs;

	$ret_json = json_encode($obj);

	return $ret_json;

  }


  public static function getCCTVTop()
  {
	  // Create a new query object.
	  $db = JFactory::getDbo();
	  $query = $db->getQuery(true);

	  // Select the required fields from the table.
	  $query
		 ->select('DISTINCT a.*');

	  $query->from('`#__top_table` AS a');

	  $query->where('a.state = 1');
	  $query->where('a.note1 = '.self::$page);

	  $query->where('a.type = '.self::$cctv_type);


	  $db->setQuery($query);

	  $items = (array) $db->loadObjectList();

	  return $items;

  }

	public static function getBaList($ip='')
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__urllist_table` AS a');

		$query->where('a.state = 1');
		$query->where('a.type = 1');
		if($ip != '')
		{
			$query->where('a.url = '.$db->quote($ip));
		}

		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
		 //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;
	}
	public static function getCCTVDev()
	{
		$param = '/index.php?option="com_floor&task=sroots.getCCTVDev" --connect-timeout 3 -o /tmp/GETCCTVDEV > /dev/null 2> /dev/null';

		$cmd = 'curl http://127.0.0.1'.$param;
		system($cmd);

		$json = file_get_contents("/tmp/GETCCTVDEV");

	  return $json;
	}

  public static function get_ba_temp_weather_info()
  {
		$ips = self::getBaList();
		$param = '/index.php?option="com_floor&task=sroots.get_temp_weather_info" -k --connect-timeout 3 -o /tmp/INFO > /dev/null 2> /dev/null';
		foreach($ips as $i=>$ip)
		{
			$cmd = 'curl https://'.$ip->url.$param;
			system($cmd);
			break;
		}

		$myfile = fopen("/tmp/INFO", "r") or die("Unable to open file!");
		$json = fread($myfile,filesize("/tmp/INFO"));
		fclose($myfile);

    return $json;
  }
	public static function get_temp_weather_info()
	{
    $json[] = self::get_main_info();

    $json[] = self::get_weather();

		$ret_json = json_encode($json,true);

		return $ret_json;

	}
	public static function get_main_info()
  {
		$items = self::get_main_temp();
		$obj = new stdClass;
		$obj->temp = '?';
		$obj->humidity = '?';
		$obj->info = '';
		if(count($items))
		{
				$obj->temp = $items[0]->temp;
				$obj->humidity = $items[0]->humidity;
				$obj->info = $items[0]->info;

		}

		$items = self::get_main_elec();
		$obj->elec_kwh = '?';
		$obj->elec_kw = '?';
		$obj->elec_v = '?';
		$obj->elec_a = '?';
		$obj->elec_f = '?';
		$obj->elec_pf = '?';
		if(count($items))
		{
			$obj->elec_kwh = $items[0]->elec_kwh;
			$obj->elec_kw = $items[0]->elec_kw;
			$obj->elec_v = $items[0]->elec_v;
			$obj->elec_a = $items[0]->elec_a;
			$obj->elec_f = $items[0]->elec_f;
			$obj->elec_pf = $items[0]->elec_pf;
		}


		$items = self::get_main_water_meters();
		$obj->total_water_usages = [];
		if(count($items))
		{
			for ($i=0; $i < count($items) ; $i++) {
				$usages = new StdClass();
				$usages->no = $items[$i]->main;
				$usages->name = $items[$i]->info;
				$usages->usage = self::get_yesterday_water_meter_usage($items[$i]->id);
				array_push($obj->total_water_usages, $usages);
				# code...
			}
		}
		// $items = self::get_main_solar_meter();
		$obj->solar_power_today = 'NA';
		$obj->solar_power_accumulate = 'NA';
		if(count($items))
		{
			$obj->solar_power_today = self::get_solar_power_today();
			$obj->solar_power_accumulate = self::get_solar_power_accumulate();
		}
		$json = json_encode($obj);

		return $json;

  }
	public static function get_main_solar_meter()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select('DISTINCT a.*');

		$query->from('`#__device_table` AS a');

		$query->where('a.state = 1');
		$query->where('a.main = 1');
		// $query->where('a.dio_type = '.self::$rs485_elec_device);
		// $query->where('a.dio_type'.' IN '.$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic)));
		$query->where('a.dio_type IN ('.implode(',',$db->quote(self::get_solar_power_device_dio_types())).")");
		//JLog::add($query, JLog::INFO, 'jerror');
		$db->setQuery($query);
		//echo($query);
		//JLog::add($query, JLog::INFO, 'jerror');

		$items = (array) $db->loadObjectList();

		return $items;
	}
	public static function get_solar_power_device_dio_types()
	{
		return array(19,33,37);
	}
	public static function get_main_water_meters()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__device_table` AS a');

		$query->where('a.state = 1');
		$query->where('a.main > 0');
		// $query->where('a.dio_type = '.self::$rs485_elec_device);

		$query->where('a.dio_type IN ('.implode(',',$db->quote(array(self::$rs485_water_meter_device,self::$rs485_tkd_water_meter_device,self::$rs485_general_opc_water_meter_device))).")");
		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
		 //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;
	}
	public static function get_solar_power_accumulate()
	{
		try
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query
				// ->select('DISTINCT device.*')
				->select('sum(device.elec_kwh) as elec_kwh')
				->from('`#__device_table` AS device')
				->where('device.state = 1')
				// ->where('device.enable = 1')
				->where('device.main = 1')
				->where('device.dio_type IN ('.implode(',',$db->quote(self::get_solar_power_device_dio_types())).")");

			$db->setQuery($query);
			$sum_record = ((array) $db->loadObjectList())[0];
			if (count($sum_record) == 0) return 'N/A';
			$sum = $sum_record->elec_kwh;
			if ($sum != null)
				return $sum;

		} catch (Exception $th) {
			//throw $th;
			return $th->getMessage();
		}
		return 'N/A';
	}
	public static function get_solar_power_today()
	{
		try
		{

			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query
				->select('DISTINCT device.*')
				->from('`#__device_table` AS device')
				->where('device.state = 1')
				// ->where('device.enable = 1')
				->where('device.main = 1')
				->where('device.dio_type IN ('.implode(',',$db->quote(self::get_solar_power_device_dio_types())).")");
			$db->setQuery($query);

			$devices = ((array) $db->loadObjectList());
			if (count($devices) == 0) return 'N/A';
			$yesterday = new DateTime('yesterday',new DateTimeZone('Asia/Taipei'));
			$q = 'SELECT * FROM (SELECT * FROM `#__electronic_meter_history` WHERE ' .
			' YEAR = '. $db->quote($yesterday->format('Y')) .
			' AND MONTH = ' . $db->quote($yesterday->format('m')) .
			' AND date_of_month = ' . $db->quote($yesterday->format('d')) .
			' AND device_id IN ('.implode(',',$db->quote(array_map(function($dev){
				return $dev->id;
				},$devices))).')' .
			' ORDER BY hour_of_day desc LIMIT 999999999) AS histories GROUP BY device_id';
			// return $q;
			$db->setQuery($q);
			$yesterday_latest_records = (array) $db->loadObjectList();
			$today = new DateTime('today',new DateTimeZone('Asia/Taipei'));
			$q = 'SELECT * FROM (SELECT * FROM `#__electronic_meter_history` WHERE ' .
			' YEAR = '. $db->quote($today->format('Y')) .
			' AND MONTH = ' . $db->quote($today->format('m')) .
			' AND date_of_month = ' . $db->quote($today->format('d')) .
			' AND device_id IN ('.implode(',',$db->quote(array_map(function($dev){
				return $dev->id;
				},$devices))).')' .
			' ORDER BY hour_of_day desc LIMIT 999999999) AS histories GROUP BY device_id';
			// return $q;
			$db->setQuery($q);
			$sum = 0;
			$today_latest_records = (array) $db->loadObjectList();
			foreach ($today_latest_records as $key => $record2) {
				foreach ($yesterday_latest_records as $key => $record1) {
					if ($record2->device_id == $record1->device_id)
					{
						$sum += $record2->accumulatepower - $record1->accumulatepower;
						break;
					}
				}
			}
			return round($sum,3);
		} catch (Exception $th) {
			//throw $th;
			return $th->getMessage();
		}
		return 'N/A';
	}
	public static function get_yesterday_water_meter_usage($device_id)
	{
		try {
			$yesterday = new DateTime('yesterday',new DateTimeZone('Asia/Taipei'));
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				->select('DISTINCT history.*');

			$query->from('`#__water_meter_history` AS history');

			$query->where('history.state = 1')
				->where('history.device_id ='. $db->quote($device_id))
				->where('history.year = '. $db->quote($yesterday->format('Y')))
				->where('history.month = '. $db->quote($yesterday->format('m')))
				->where('history.date_of_month = '. $db->quote($yesterday->format('d')))
				->order('history.device_id DESC')
				->order('history.hour_of_day DESC')
				->setLimit(1);
			$db->setQuery($query);

			$maxs = (array) $db->loadObjectList();
			$query = $db->getQuery(true);

			$query
				->select('DISTINCT history.*');

			$query->from('`#__water_meter_history` AS history');

			$query->where('history.state = 1')
				->where('history.device_id ='. $db->quote($device_id))
				->where('history.year = '. $db->quote($yesterday->format('Y')))
				->where('history.month = '. $db->quote($yesterday->format('m')))
				->where('history.date_of_month = '. $db->quote($yesterday->format('d')))
				->order('history.hour_of_day ASC')
				->setLimit(1);
			$db->setQuery($query);

			$mins = (array) $db->loadObjectList();
			if (count($maxs) && count(mins))
			{
				return $maxs[0]->accumulatewaterflow - $mins[0]->accumulatewaterflow;
			}
			return 0;
		} catch (Exception $th) {
			//throw $th;
		}
		return 'N/A';
	}
	public static function get_main_elec()
	{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.main = 1');
			// $query->where('a.dio_type = '.self::$rs485_elec_device);
			// $query->where('a.dio_type'.' IN '.$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic)));
			$query->where('a.dio_type IN ('.implode(',',$db->quote(array(
				self::$rs485_elec_device,
				self::$rs485_elec_device_cic,
				self::$rs485_elec_device_acuvim,
				self::$rs485_elec_device_tatung,
				self::$rs485_elec_device_pem333,
				self::$rs485_elec_device_pem575,
				self::$rs485_elec_device_shihlin,
				self::$rs485_elec_device_cicbaw1a2a,
				self::$rs485_elec_device_cicbaw2c,
				self::$rs485_elec_device_weema_1p,
				self::$rs485_elec_device_weema_3p,
				self::$rs485_elec_device_opcda,
				self::$rs485_elec_device_general_opcda,
				self::$rs485_elec_device_aem_drb,
				self::$rs485_solar_device_primevolt,
				self::$rs485_solar_device_shihlinspm8,
				self::$rs485_solar_device_general,
				self::$rs485_vmr_mp7_elec_device,
				self::$rs485_vmr_mp8_elec_device,
				self::$rs485_elec_device_m4m
				))).")");
			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;


	}

	public static function get_main_temp()
	{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.main = 1');
			$query->where('a.dio_type in (4,11,28,38,39)');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

	}
	public static function get_weather()
	{
	  $items = self::get_weather_items();

	  $key=$items[0]->key;
	  $city=self::$weather_cities[$items[0]->city];

	  $weatherUrl = "wget https://opendata.cwb.gov.tw/api/v1/rest/datastore/F-C0032-001?Authorization=\"".$key."&locationName=".$city."&elementName=Wx,MinT,MaxT\"" . " -t3 --connect-timeout=60 -o /tmp/weather1 -O /tmp/WEATHER1";

	  system($weatherUrl);

	  $myfile = fopen("/tmp/WEATHER1", "r") or die("Unable to open file!");
	  // Output one line until end-of-file
	  $json = "";
	  while(!feof($myfile)) {
	    $json = $json . fgets($myfile);

	  }
	  fclose($myfile);

    return $json;



	}
	public static function get_weema_package_version()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);
		$query->select('DISTINCT extension.*')
				->from('`#__extensions` AS extension')
				->where('extension.element = \'pkg_weema\'')
				->where('extension.type = \'package\'');
		$db->setQuery($query);
		$items = (array) $db->loadObjectList();
		if (isset($items) && !empty($items)) {

			return json_decode($items[0]->manifest_cache)->version;
		}
		return 0;
	}
	public static function get_home_site_settings()
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);
		$query->select('DISTINCT setting.*')
				->from('`#__site_settings` AS setting')
				->where('setting.state =1')
				->where('setting.enable = 1')
				->where('setting.name = \'HOME_WIDGET\'');
			$db->setQuery($query);

			$items = (array) $db->loadObjectList();
			if (isset($items) && !empty($items)) {
				$items[0]->content = json_decode($items[0]->content);
				return $items[0];
			}
			else {
				$fake_default = new stdClass();
				$fake_default->name = 'HOME_WIDGET';
				$fake_default->content = new stdClass();
				$fake_default->content->mode = 0;
			}
	}
	public static function get_weather_items()
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__weather_table` AS a');

		$query->where('a.state = 1');

		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
		 //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;
	}

	public static function getMyAccount()
	{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__myaccount_table` AS a');

		 $query->where('a.state = 1');

		 $query->order($db->escape('id DESC'));

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

	}

	public static function delete_item($obj)
	{
			$db = JFactory::getDbo();

			// Create a new query object.
			$query = $db->getQuery(true);

			$conditions = array(
					$db->quoteName('id') . ' = ' . $obj->id,
			);

			$query->delete($db->quoteName($obj->table));
			$query->where($conditions);

			$db->setQuery($query);

			$db->execute();
			//JLog::add($query, JLog::WARNING, 'jerror');


	}

	public static function change_old_limit_item($obj)
	{
      $db = JFactory::getDbo();

			$fields = array(
				$db->quoteName('name') . ' = ' . $db->quote($obj->name),
        $db->quoteName('enable') . ' = ' . $db->quote($obj->enable),
        $db->quoteName('start_hour') . ' = ' . $db->quote($obj->start_hour),
        $db->quoteName('start_min') . ' = ' . $db->quote($obj->start_min),
        $db->quoteName('end_hour') . ' = ' . $db->quote($obj->end_hour),
        $db->quoteName('end_min') . ' = ' . $db->quote($obj->end_min),
				$db->quoteName('limit') . ' = ' . $db->quote($obj->limit),
			);


			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id)
			);
      $query = $db->getQuery(true);
      $query->update($db->quoteName('#__limit_table'))->set($fields)->where($conditions);

	    $db->setQuery($query);

			$db->execute();

	}

	public static function sendToUrl($obj)
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($obj->arr));
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		foreach($obj->ips as $i=>$ip)
		{
			$url = "http://".$ip->url.'/index.php';

			curl_setopt($ch, CURLOPT_URL, $url);

			$output = curl_exec($ch);

			$file = fopen($obj->filename1.$ip->url,"w");
			fwrite($file,$ip->url." ".print_r($obj->arr,true));
			fclose($file);

			$file = fopen($obj->filename.$ip->url,"w");
			fwrite($file,$output);
			fclose($file);

		}

		curl_close($ch);

	}

	public static function send_del_alarm()
	{
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.del_alarm",
		);

		$filename = "/tmp/del_alarm";
		$filename1 = "/tmp/del_alarm1";
		$ips = self::getCenterList();

		$obj = new stdClass;

		$obj->arr = $arr;
		$obj->filename = $filename;
		$obj->filename1 = $filename1;
		$obj->ips = $ips;

		self::sendToUrl($obj);


	}

	public static function send_limit_alarm()
	{
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.limit_alarm",
		);

		$filename = "/tmp/limit_alarm";
		$filename1 = "/tmp/limit_alarm1";
		$ips = self::getCenterList();

		$obj = new stdClass;

		$obj->arr = $arr;
		$obj->filename = $filename;
		$obj->filename1 = $filename1;
		$obj->ips = $ips;

		self::sendToUrl($obj);


	}
	public static function send_out_alarm()
	{
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.out_alarm",
		);

		$filename = "/tmp/out_alarm";
		$filename1 = "/tmp/out_alarm1";
		$ips = self::getCenterList();

		$obj = new stdClass;

		$obj->arr = $arr;
		$obj->filename = $filename;
		$obj->filename1 = $filename1;
		$obj->ips = $ips;

		self::sendToUrl($obj);


	}
	public static function send_passwd_alarm()
	{
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.passwd_alarm",
		);

		$filename = "/tmp/passwd_alarm";
		$filename1 = "/tmp/passwd_alarm1";
		$ips = self::getCenterList();

		$obj = new stdClass;

		$obj->arr = $arr;
		$obj->filename = $filename;
		$obj->filename1 = $filename1;
		$obj->ips = $ips;

		self::sendToUrl($obj);


	}
	public static function send_username_alarm()
	{
		$arr = array(
			"option" => "com_floor",
			"task"   => "sroots.username_alarm",
		);

		$filename = "/tmp/username_alarm";
		$filename1 = "/tmp/username_alarm1";
		$ips = self::getCenterList();

		$obj = new stdClass;

		$obj->arr = $arr;
		$obj->filename = $filename;
		$obj->filename1 = $filename1;
		$obj->ips = $ips;

		self::sendToUrl($obj);


	}
	public static function getCenterList()
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__urllist_table` AS a');

		$query->where('a.state = 1');
		$query->where('a.type = 2');

		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
		 //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;
	}

  public static function set_open_path($item,$db,$open,$i,$fields)
  {
		if($open != 1)
		    return $fields;

		if($i == 0)
		{
			$isvideo = $item->isvideo;
			$path = $item->path;
			$num = '';

		}
		else if($i == 1)
		{
			$isvideo = $item->isvideo1;
			$path = $item->path1;
			$num = '1';

		}
		else if($i == 2)
		{
			$isvideo = $item->isvideo2;
			$path = $item->path2;
			$num = '2';

		}
		else if($i == 3)
		{
			$isvideo = $item->isvideo3;
			$path = $item->path3;
      $num = '3';
		}

		if($isvideo)
		{
			if($open == 1)
			{
				$field = array(
					$db->quoteName('path'.$num) . ' = ' . $db->quote($path),
					$db->quoteName('open'.$num) . ' = ' . $db->quote($open)

				);

				$fields = array_merge($fields,$field);


			}


		}

    return $fields;

  }

	public static function reset_info_msg($obj)
	{
		$db = JFactory::getDbo();

		$query = $db->getQuery(true)
		->update($db->quoteName('#__info_table'))

		//->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		->set($db->quoteName('enable') . ' = ' . $db->quote(0))

		->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		$db->setQuery($query)->execute();
		//echo($query);



 }

	public static function get_info_msg()
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		$query
			 ->select('DISTINCT a.*');

		$query->from('`#__info_table` AS a');

		$query->where('a.state = 1');

		$query->where('a.enable = 1');

		 //JLog::add($query, JLog::INFO, 'jerror');
		 $db->setQuery($query);
		 //echo($query);
		 //JLog::add($query, JLog::INFO, 'jerror');

		 $items = (array) $db->loadObjectList();

		 return $items;


 }

	public static function send_info_msg($obj)
	{
		$db = JFactory::getDbo();
		$db->setQuery('SELECT MAX(ordering) FROM #__info_table');
		$max = $db->loadResult();
		$ordering = $max+1;

		$created_by = JFactory::getUser()->name;
		$checked_out = false;
		$state = 1;

		// Create a new query object.
		$query = $db->getQuery(true);

		// Insert columns.
		$columns = array(
					'ordering',
					'state',
					'checked_out',
					'created_by',
					'enable',
					'message',

			);

		 // Insert values.
		 $values = array(
					$db->quote($ordering),
					$db->quote($state),
					$db->quote($checked_out),
					$db->quote($created_by),
					$db->quote($obj->enable),
					$db->quote($obj->msg_context),

					);

			// Prepare the insert query.
			$query
					->insert($db->quoteName('#__info_table'))
					->columns($db->quoteName($columns))
					->values(implode(',', $values));

					// Set the query using our newly populated query object and execute it.
					$db->setQuery($query);
					$db->execute();


	}

	public static function update_acc($obj)
	{
		$obj->id = 1;

		$db = JFactory::getDbo();

		$fields = array(

			$db->quoteName('ftp_enable') . ' = ' . $db->quote($obj->enable),
			$db->quoteName('ftp_server') . ' = ' . $db->quote($obj->server),
			$db->quoteName('ftp_port') . ' = ' . $db->quote($obj->port),
			$db->quoteName('ftp_user') . ' = ' . $db->quote($obj->user),
			$db->quoteName('ftp_passwd') . ' = ' . $db->quote($obj->passwd),
			$db->quoteName('sms_username') . ' = ' . $db->quote($obj->sms_username),
			$db->quoteName('sms_passwd') . ' = ' . $db->quote($obj->sms_passwd),
			$db->quoteName('rec_sec') . ' = ' . $db->quote($obj->rec_sec),

			$db->quoteName('del_alarm_enable') . ' = ' . $db->quote($obj->del_alarm_enable),
			$db->quoteName('del_alarmdir') . ' = ' . $db->quote($obj->del_alarmdir),
			$db->quoteName('del_alarm') . ' = ' . $db->quote($obj->del_alarm),

			$db->quoteName('limit_alarm_enable') . ' = ' . $db->quote($obj->limit_alarm_enable),
			$db->quoteName('limit_alarmdir') . ' = ' . $db->quote($obj->limit_alarmdir),
			$db->quoteName('limit_alarm') . ' = ' . $db->quote($obj->limit_alarm),

			$db->quoteName('out_alarm_enable') . ' = ' . $db->quote($obj->out_alarm_enable),
			$db->quoteName('out_alarmdir') . ' = ' . $db->quote($obj->out_alarmdir),
			$db->quoteName('out_alarm') . ' = ' . $db->quote($obj->out_alarm),

			$db->quoteName('passwd_error_alarm_enable') . ' = ' . $db->quote($obj->passwd_error_alarm_enable),
			$db->quoteName('passwd_error_alarmdir') . ' = ' . $db->quote($obj->passwd_error_alarmdir),
			$db->quoteName('passwd_error_alarm') . ' = ' . $db->quote($obj->passwd_error_alarm),

			$db->quoteName('username_error_alarm_enable') . ' = ' . $db->quote($obj->username_error_alarm_enable),
			$db->quoteName('username_error_alarmdir') . ' = ' . $db->quote($obj->username_error_alarmdir),
			$db->quoteName('username_error_alarm') . ' = ' . $db->quote($obj->username_error_alarm),
			$db->quoteName('elec_alarm') . ' = ' . $db->quote($obj->elec_alarm),

		);

		$conditions = array(
				$db->quoteName('id') . ' = ' . $db->quote($obj->id),

		);
		$query = $db->getQuery(true);
		$query->update($db->quoteName('#__myaccount_table'))->set($fields)->where($conditions);

		$db->setQuery($query)->execute();

	}
	public static function thisHourTimestamp($hour,$day)
	{
		$year = date("Y");
		$month = date("m");
	    $d = DateTime::createFromFormat(
			'Y-m-d H:i:s',
			$day.' '.$hour.':00:00',

	        new DateTimeZone('Asia/Taipei')
	    );

	    if ($d === false) {
			echo($day.' '.$hour.':00:00');

	        die("Incorrect date string");
	    } else {
	        //echo $d->getTimestamp();
	    }
			return $d->getTimestamp();
  }

	public static function update_elec_all()
	{

			$obj = new stdClass;

			$obj->year = Date('Y');
			$obj->month = Date('m');
			$obj->day = Date('d');
			$obj->update = Date('Y-m-d H:i:s');

			$obj->hour = 0;
			$items = self::getAllElec();


			foreach($items as $item)
			{
					$obj->id = $item->dev_id;
					self::calcDay($obj);
					//self::calcMonth($obj);
					//self::calcYear($obj);

			}

	}

	public static function calcDay($obj)
	{
			$items = self::getDayHourItems($obj);

			$obj->type = self::$day_type;
			self::doUpdateDayItem($obj,$items);

	}

	public static function getDayHourItems($obj)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
			 ->select('DISTINCT a.*');

	 $query->from('`#__elec_table` AS a');

	 $query->where('a.state = 1');

	 $query->where('a.year = '.$obj->year);
	 $query->where('a.month = '.$obj->month);
	 $query->where('a.day = '.$obj->day);
	 $query->where('a.type = '.self::$hour_type);

	 $query->where('a.dev_id = '.$obj->id);

	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;



	}

	public static function doUpdateDayItem($obj,$items)
	{
		$obj->hour = 0;

		$days = self::getDayItems($obj);

		if(count($days) == 0)
		{
				self::createDayItem($obj);
				$days = self::getDayItems($obj);
		}


		$day = $days[0];

		$obj->elec_kwh_cur = 0;
		$day->elec_kwh = 0;

		foreach($items as $item)
		{
				//$day->elec_kwh = (float)($day->elec_kwh)+(float)($item->elec_kwh);

				$obj->elec_kwh_cur = $item->elec_kwh_cur;

		}

		$day->elec_kwh_cur = $obj->elec_kwh_cur;

		self::updateDayItem($day);

	}

	public static function getAllElec()
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
			 ->select('DISTINCT a.*');

	 $query->from('`#__elec_table` AS a');

	 $query->where('a.state = 1');

	 $query->group('a.dev_id');

	 $db->setQuery($query);

	 //echo($query);
	 $items = (array) $db->loadObjectList();

	 return $items;



	}
	public static function calcMonth($obj)
	{

			$obj->type = self::$day_type;
			$items = self::getDayItems($obj);

			$obj->type = self::$month_type;
			$obj->day = 1;
			$obj->hour = 0;

			$months = self::getDayItems($obj);

      if(count($months) == 0)
			{
					self::createDayItem($obj);
					$months = self::getDayItems($obj);
			}

			$month = $months[0];

			$month->elec_kwh = 0;
			$month->elec_kwh_cur = 0;

			foreach($items as $item)
			{
				$month->elec_kwh = (float)($month->elec_kwh)+(float)($item->elec_kwh);
				$month->elec_kwh_cur = $item->elec_kwh_cur;

			}

			self::updateDayItem($month);

	}
	public static function getDayItems($obj)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
			 ->select('DISTINCT a.*');

	 $query->from('`#__elec_table` AS a');

	 $query->where('a.state = 1');

	 $query->where('a.year = '.$obj->year);
	 $query->where('a.month = '.$obj->month);
	 $query->where('a.day = '.$obj->day);

	 if($obj->type == self::$hour_type)
	 {
		 $query->where('a.hour = '.$obj->hour);

	 }

	 $query->where('a.type = '.$obj->type);

	 $query->where('a.dev_id = '.$obj->id);

	 $db->setQuery($query);

	 $items = (array) $db->loadObjectList();

	 return $items;



	}

	public static function createDayItem($obj)
	{
		$db = JFactory::getDbo();
		$db->setQuery('SELECT MAX(ordering) FROM #__elec_table');
		$max = $db->loadResult();
		$ordering = $max+1;

		$created_by = JFactory::getUser()->name;
		$checked_out = false;
		$state = 1;

		// Create a new query object.
		$query = $db->getQuery(true);


		$a = strptime($obj->day.'-'.$obj->month.'-'.$obj->year, '%d-%m-%Y');
		$timestamp = mktime($obj->hour, 0, 0, $a['tm_mon']+1, $a['tm_mday'], $a['tm_year']+1900);

		// Insert columns.

		$columns = array(
						'ordering',
						'state',
						'checked_out',
						'created_by',
						'year',
						'month',
						'day',
						'type',
						'dev_id',
						'elec_kwh',
						'timestamp',
						'hour',
						'update',
					);

		// Insert values.
		$values = array(
					 $db->quote($ordering),
					 $db->quote($state),
					 $db->quote($checked_out),
					 $db->quote($created_by),

					 $db->quote($obj->year),
					 $db->quote($obj->month),
					 $db->quote($obj->day),
					 $db->quote($obj->type),
					 $db->quote($obj->id),
					 $db->quote(0),
					 $db->quote($timestamp),
					 $db->quote($obj->hour),
					 $db->quote($obj->update),

					 );



		// Prepare the insert query.
		$query
					->insert($db->quoteName('#__elec_table'))
					->columns($db->quoteName($columns))
					->values(implode(',', $values));

			// Set the query using our newly populated query object and execute it.
			$db->setQuery($query);
			//echo($query);
			$db->execute();

	}

	public static function updateDayItem($obj)
	{
			$db = JFactory::getDbo();

			$query = $db->getQuery(true)
			->update($db->quoteName('#__elec_table'))

			//->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
			->set($db->quoteName('elec_kwh') . ' = ' . $db->quote($obj->elec_kwh))
			->set($db->quoteName('elec_kwh_cur') . ' = ' . $db->quote($obj->elec_kwh_cur))

			->set($db->quoteName('update') . ' = ' . $db->quote($obj->update))

			->where($db->quoteName('type') . ' = ' . $db->quote($obj->type))

			->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

			$db->setQuery($query)->execute();
			//echo($query);


	}

	public static function getLoginlog_fail($timestamp,$fail_id)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__loginlog_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.action_id = '.$fail_id);

	 $query->where('a.timestamp > '.$db->quote($timestamp));

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function check_passwd_error_count()
	{
		$accs = self::getMyAccount();

		if(count($accs) == 0)    return;

    $acc = $accs[0];

		if($acc->passwd_error_alarm_enable == 0)    return;

    $now_timestamp = strtotime("now");

    $timestamp = (int)$now_timestamp-($acc->passwd_error_alarm_min)*60;

		$items = self::getLoginlog_fail($timestamp,self::$passwd_error);

    $count = count($items);

		if($count >= $acc->passwd_error_alarm_limit)
		{
			self::send_passwd_alarm();
		}

	}

	public static function check_username_error_count()
	{
		$accs = self::getMyAccount();

		if(count($accs) == 0)    return;

    $acc = $accs[0];
		if($acc->username_error_alarm_enable == 0)    return;

    $now_timestamp = strtotime("now");

    $timestamp = $now_timestamp-($acc->username_error_alarm_min)*60;
		$items = self::getLoginlog_fail($timestamp,self::$username_error);

    $count = count($items);

		if($count >= $acc->username_error_alarm_limit)
		{
			self::send_username_alarm();
		}




	}

	public static function getLimit($name)
	{
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
	 $query
	 ->select('DISTINCT a.*');

	 $query->from('`#__limit_table` AS a');

	 $query->where('a.state = 1');
	 $query->where('a.enable = 1');
	 $query->where('a.name = '.$db->quote($name));

	 $db->setQuery($query);

	 //JLog::add($query, JLog::INFO, 'jerror');
	 $items = (array) $db->loadObjectList();

	 return $items;
	}

	public static function is_need_logout($name)
	{
		$items = self::getLimit($name);

		if(count($items) == 0)    return false;

		$item = $items[0];

		$os_not = false;
		if($item->start_hour > $item->end_hour)
		{
			$high = (int)$item->start_hour*100+(int)$item->start_min;
			$low = (int)$item->end_hour*100+(int)$item->end_min;
			$is_not = true;
		}
		else
		{
			$low = (int)$item->start_hour*100+(int)$item->start_min;
			$high = (int)$item->end_hour*100+(int)$item->end_min;
		}

		$h = date('H');
		$m = date('i');

		$date = (int)$h*100+(int)$m;

		//JLog::add($date." A ".$low." A ".$high." A ".$is_not, JLog::INFO, 'jerror');

		if($date > $low && $date < $high)
		{
			if($is_not)
			{
				return true;
			}

		}
		else
		{
			if(!$is_not)
			{
				return true;
			}
		}
	 return false;
	}

	public static function show_msg($msg)
	{
    $txt = "<!DOCTYPE html>\n";
		$txt = $txt."<html>\n";

		$txt = $txt."<head>\n";
		$txt = $txt."<title>Page Title</title>\n";
		$txt = $txt."</head>\n";
		$txt = $txt."<body>\n";
		$txt = $txt."<h1>$msg</h1>\n";
		$txt = $txt."</body>\n";
		$txt = $txt."</html>\n";

    echo($txt);
	}
	public static function getPublicIP()
	{
		$items = FloorHelpersFloor::getSIPINFO();

		if(count($items) <= 0)
		{
      		return "";
		}

	  	$arr["name"] = $items[0]->username;
		$arr["password"] = $items[0]->password;

	    $json = json_encode($arr);

		$addr = "http://".$items[0]->serverip .":8899/api/account/credentials/verify";
        $file = "/var/www/html/POST";

		$cmd = 'wget -T 2  -O ' . $file . " --post-data='" . $json . "' " . $addr;

		system($cmd);

	  	$content = JFile::read($file);
		if(empty($content))
		{
			return "";
		}

		$cmd = 'curl -v -X GET http://'.$items[0]->serverip .':8899/api/call_manager/summary ';
		$cmd = $cmd.' -H "Content-Type: application/json" > '.$file;

		$myjson = json_decode($content,true);
		$cmd = $cmd.' -H "access_token: '.$myjson["access_token"].'"';

		system($cmd);

		$content = JFile::read($file);
		return $content;

	}

}
