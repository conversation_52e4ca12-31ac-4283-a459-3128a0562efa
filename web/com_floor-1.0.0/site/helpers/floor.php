<?php

/**
 * @version    CVS: 1.0.0
 * @package    Com_Floor
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  2018 kevin lo
 * @license    GNU General Public License version 2 or later; see LICENSE.txt
 */
defined('_JEXEC') or die;

JLoader::register('FloorHelper', JPATH_ADMINISTRATOR . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_floor' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'floor.php');

use \Joomla\CMS\Factory;
use \Joomla\CMS\MVC\Model\BaseDatabaseModel;

require_once(JPATH_SITE . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'com_top' . DIRECTORY_SEPARATOR . 'helpers' . DIRECTORY_SEPARATOR . 'toplog.php');

/**
 * Class FloorFrontendHelper
 *
 * @since  1.6
 */
class FloorHelpersFloor
{
	public static $page = 1;
	public static $bpage = 2;
	public static $dpage = 3;
	public static $apage = 4;
	public static $cpage = 4;
	public static $adpage = 5;
	public static $tdiopage = 6;
	public static $tdpage = 7;
	public static $all_group = 8;
	public static $iptype = 9;
	public static $cdpage = 11;
	public static $tvdpage = 12;
	public static $tvpage = 13;
	public static $ddiopage = 14;
	public static $rs485page = 15;
	public static $doorpage = 16;

	public static $alarm_msg = 0;
	public static $sms_msg = 1;

	public static $msg_text = 1;
	public static $msg_audio = 2;
	public static $msg_softphone = 3;
	public static $msg_email = 4;

	public static $types = array(

	 	2=>'ip 裝置',
	 	1=>'網路電話',
   		3=>'煙霧偵測',

	);
	public static $dio_vendors = array(

		1=>'weema',
		2=>'研華',
		3=>'昕暘',
		4=>'SOYAL',
		5=>'泓格',
		7=>'ModbusTCP(Coil for DI/DO)',
		8=>'六川電梯',
		9=>'永揚消防',
		10=>'保創消防',
		11=>'三菱電梯',
		13=>'大業電梯',
		101=>'door',

	);
	public static $status = array(

	  	0=>'待機',
		1=>'待機',
	  	2=>'使用中',
    	3=>'離線',
	  	100=>'結束',
   		101=>'開始',
		102=>'條件觸發',
		103=>'條件持續時間',
		104=>'超時',
		105=>'警報',
		106=>'持續時間',
		107=>'開門',
		108=>'失聯',
		1001=>'強制撥出',
	);

	public static $dio_types = array(

		1=>'DI',
		2=>'DO',

	);

	public static $dio_values = array(

		0=>'NO',
		1=>'NC',

	);

	public static $fontsizes = array(

		3=>'3',
		4=>'4',
		5=>'5',
		6=>'6',
		7=>'7',
	   	8=>'8',
		9=>'9',
		10=>'10',

	);

	public static $colors = array(

	   	'black'=>'黑',
		'red'=>'紅',
		'orange'=>'橙',
		'yellow'=>'蕢',
		'green'=>'綠',
		'blue'=>'藍',
		'indigo'=>'靛',
		'purple'=>'紫',

	);

	public static $building_normal = 0;
	public static $building_elec = 1;

	public static $rs485_temp_device = 4;
	public static $rs485_elec_device = 5;
	public static $rs485_elec_devic_cic = 9;
	public static $rs485_elec_devic_daepm210 = 44;
	public static $rs485_elec_devic_m4m = 50;
	public static $rs485_elec_devic_kt_mk3 = 51;
	public static $rs485_elec_devic_acuvim = 43;
	public static $rs485_elec_devic_tatung = 10;
	public static $rs485_co_device = 6;

	public static $SOYAL_VENDOR = 4;

	public static $year_type=2;
	public static $season_type=3;
	public static $week_type=4;
	public static $month_type=5;
	public static $day_type=6;
	public static $hour_type=7;

	public static $public_area=2;


	/**
	 * Get an instance of the named model
	 *
	 * @param   string  $name  Model name
	 *
	 * @return null|object
	 */
	public static function getModel($name)
	{
		$model = null;

		// If the file exists, let's
		if (file_exists(JPATH_SITE . '/components/com_floor/models/' . strtolower($name) . '.php'))
		{
			require_once JPATH_SITE . '/components/com_floor/models/' . strtolower($name) . '.php';
			$model = BaseDatabaseModel::getInstance($name, 'FloorModel');
		}

		return $model;
	}

	/**
	 * Gets the files attached to an item
	 *
	 * @param   int     $pk     The item's id
	 *
	 * @param   string  $table  The table's name
	 *
	 * @param   string  $field  The field's name
	 *
	 * @return  array  The files
	 */
	public static function getFiles($pk, $table, $field)
	{
		$db = Factory::getDbo();
		$query = $db->getQuery(true);

		$query
			->select($field)
			->from($table)
			->where('id = ' . (int) $pk);

		$db->setQuery($query);

		return explode(',', $db->loadResult());
	}

    /**
     * Gets the edit permission for an user
     *
     * @param   mixed  $item  The item
     *
     * @return  bool
     */
    public static function canUserEdit($item)
    {
        $permission = false;
        $user       = Factory::getUser();

        if ($user->authorise('core.edit', 'com_floor'))
        {
            $permission = true;
        }
        else
        {
            if (isset($item->created_by))
            {
                if ($user->authorise('core.edit.own', 'com_floor') && $item->created_by == $user->id)
                {
                    $permission = true;
                }
            }
            else
            {
                $permission = true;
            }
        }

        return $permission;
    }
		public static function test_utility()
		{
			return TopHelpersUtility::getMyAccount();

		}
		public static function resendEvent($items,$json,$token)
    {
			foreach($items as $i=>$item)
			{
				  if($item->url == "127.0.0.1")
					    continue;
			    	$addr = "http://".$item->url .'/index.php?option="com_floor&task=sroots.'.$token.'"';
	        		$LOG = "/tmp/SIPLOG";
          			$log = "/tmp/siplog";

			    	$cmd = '/usr/bin/wget -t3  -O ' . $LOG . " -o ".$log ." --post-data='" . $json . "' " . $addr . " > /dev/null 2> /dev/null ";

			    	system($cmd);


		  }

    }
    public static function MyEventRelayAll($json)
		{
			$items = TopHelpersUtility::getBaList();

      self::resendEvent($items,$json,'getMyEvent');

			$items = FloorHelpersFloor::getRelayList();
			self::resendEvent($items,$json,'getRelayEvent');

		}
		public static function getTimings()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__timing_table` AS a');

			$query->where('a.state = 1');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;
		}
    public static function update_ip2($obj)
    {

			  $dev = new stdClass;
	      foreach($obj->ids as $i=>$item)
				{
					echo($item["id"]);

					$mydevs = self::getDevice($item["id"]);
					if(count($mydevs) && ($mydevs[0]->status != $obj->status))
					{
						  $dev->id = $item["id"];
							$dev->need_alarm = 1;
							$dev->type = "IP監控";
							$dev->info = $mydevs[0]->info;
							$dev->note = $mydevs[0]->note;
							$dev->status = $obj->status;
							$dev->state = $dev->status;
							$dev->floor = $mydevs[0]->floor;
					    self::update_device_state($dev);
					    self::add_to_devicelog($dev);
				  }

				}

    }
    public static function getRecordById($id)
    {

			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__record_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.id = '.$id);

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

    }

		public static function getDevicelogByUncheck()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__devicelog_table` AS a');

		 $query->where('a.state = 1');

		 $query->where("a.trigger_alarm = '1' AND (((a.check_name IS NULL OR a.check_name = 'sip server') OR a.check_name = '') AND (a.end_date = ''))");
			// $query->where('a.trigger_alarm = 1');
		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

		}

		public static function is_need_alarm()
    {
        // $items = self::getDevicelogByUncheck();
		$items = self::findMaxLogId();
				if(count($items))    return 1;
				else return 0;
    }

		public static function update_record($obj)
		{

			$db = JFactory::getDbo();

			$query = $db->getQuery(true)
			->update($db->quoteName('#__record_table'))

			//->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
			->set($db->quoteName('path') . ' = ' . $db->quote($obj->path))
			->set($db->quoteName('title') . ' = ' . $db->quote($obj->title))
			->set($db->quoteName('member') . ' = ' . $db->quote($obj->member))

			->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

			$db->setQuery($query)->execute();

		}


		public static function send_alarm_msg($obj)
    {
        if($obj->msg_type == self::$msg_text)
				{
					$acc = TopHelpersUtility::getMyAccount();
					$obj->sms_username = $acc[0]->sms_username;
					$obj->sms_passwd = $acc[0]->sms_passwd;

					$path = urlencode($obj->msg_context);

	        $cmd ='wget http://api.twsms.com/json/sms_send.php?"username='.$obj->sms_username."&password=".$obj->sms_passwd."&mobile=".$obj->msg_number."&message=".$path.'" -t 3 -o /tmp/sms_alarm -O /tmp/SMS_ALARM > /dev/null 2> /dev/null &';
	        system($cmd);
					echo($cmd);

				}
				else if($obj->msg_type == self::$msg_email)
				{
					$obj->title = $obj->msg_name;
					$obj->path = $obj->msg_context;

					$addr = preg_split("/[\s,]+/", $obj->msg_number);

					$to = array();


					foreach($addr as $i=>$item)
					{
							echo("<> ".$item." <>");
							$arr = array($item);
							array_push($to,$item);

					}
					print_r($to);

					$obj->to = $to;

					self::do_sendemail($obj);
				}
				else if($obj->msg_type == self::$msg_softphone)
				{
					self::do_sendAlarmMessage1($obj);
				}
    }

		public static function do_sendsms($obj)
    {
			  $path = urlencode('您的家人已進社區 '.$obj->path);

			  foreach($obj->addr as $i=>$item)
				{
            $cmd ='wget http://api.twsms.com/json/sms_send.php?"username='.$obj->sms_username."&password=".$obj->sms_passwd."&mobile=".$item."&message=".$path.'" -t 3 -o /tmp/sms -O /tmp/SMS > /dev/null 2> /dev/null &';
            system($cmd);
						echo($cmd);

        }
		}
		public static function do_sendemail($obj)
    {
			$mailer = JFactory::getMailer();

			# Set sender array so that my name will show up neatly in your inbox
			//$mailer->setSender($from);

			# Add a recipient -- this can be a single address (string) or an array of addresses
			$mailer->addRecipient($obj->to);

			$mailer->setSubject($obj->title);

			$mailer->setBody($obj->path);

			$send = $mailer->send();

	    if ( $send !== true ) {
				  echo 'Error addr: '.$obj->to.' ';
	        echo 'Error sending email: ';
					echo("<p>" . $send->getMessage() . "</p>");
	    } else {
	        echo 'Mail sent';
	    }

    }
		public static function sendMail($id,$fileName,$email_addr)
		{
			$acc = TopHelpersUtility::getMyAccount();

      $items = self::get_record_item($id);

      if(count($items) && $items[0]->type == self::$alarm_msg)
			{
				$subject = "警報錄影檔";

			}
			else
			{
				$subject = "門禁錄影檔";

			}

      $addr = preg_split("/[\s,]+/", $email_addr);

			$to = array();

			print_r($addr);
			foreach($addr as $i=>$item)
			{
					echo("<> ".$item." <>");
					$arr = array($item);
					array_push($to,$item);

			}
			print_r($to);

			$body = $acc[0]->note5.$fileName.$id.".mp4";
			//$user = JFactory::getUser();

			//$from = array("<EMAIL>", "");
			# Invoke JMail Class

      $obj = new stdClass;
      $obj->to = $to;
			$obj->id = $id;
			$obj->path = $body;
			$obj->title = $subject;
			$obj->member = $email_addr;
			$obj->addr = $addr;
      $obj->sms_username = $acc[0]->sms_username;
			$obj->sms_passwd = $acc[0]->sms_passwd;

      //return;
      self::update_record($obj);

			$items = self::get_record_item($obj->id);
			if(count($items))
			{
				self::sendRecordMsg($items[0]);

        echo('item '.$items[0]->item);
				if($items[0]->item == self::$sms_msg)
				{
					self::do_sendsms($obj);

				}
				else
				{
					self::do_sendemail($obj);

				}

			}


		}
		public static function get_record_item($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__record_table` AS a');

			$query->where('a.state = 1');

			$query->where('a.id = '.$id);

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}
		public static function sendRecordMsg($item)
		{
			$ips = TopHelpersUtility::getBaList();
			$param = '/index.php?option="com_floor&task=sroots.recordMsg&title='.$item->title.'&ipcam='.$item->ipcam.'&item='.$item->item.'&name='.$item->name.'&path='.$item->path.'&member='.$item->member.'" '.'  -k --connect-timeout 60 -o /tmp/REC > /dev/null 2> /dev/null &';
			foreach($ips as $i=>$ip)
			{
				$cmd = 'curl https://'.$ip->url.$param;
				system($cmd);
			}

		}
		public static function check_dir($filename)
		{
			if (!file_exists($filename."/")) {
					mkdir($filename, 0777);
					echo "The directory $dirname was successfully created.";

			} else {
					echo "The directory $dirname exists.";
			}

		}

		public static function ftp_upload($obj)
		{
			echo("\nftp_upload\n");

			$accs = TopHelpersUtility::getMyAccount();

      $acc = $accs[0];

      if($acc->ftp_enable == 0)    return;

			echo("ftp_upload 1\n");
			$host = $acc->ftp_server;
			$port = $acc->ftp_port;
		  $user = $acc->ftp_user;
		  $pwd = $acc->ftp_passwd;

			echo($host."\n");
			echo($port."\n");
			echo($user."\n");
			echo($pwd."\n");
		  // 進行ftp連線，根據port是否設定，傳遞的引數會不同
		  if(empty($port)){
		      $f_conn = ftp_connect($host);
	  	}else{
		      $f_conn = ftp_connect($host, $port);
		  }

		  if(!$f_conn){
		      echo "connect fail\n";
		      return;
		  }
		  echo "connect success\n";
		  // 進行ftp登入，使用給定的ftp登入使用者名稱和密碼進行login
		  $f_login = ftp_login($f_conn,$user,$pwd);
		  if(!$f_login){
		      echo "login fail\n";
					ftp_close($f_conn);
		      return;
		   }

		   echo "login success\n";
		   // 獲取當前所在的ftp目錄
		   $in_dir = ftp_pwd($f_conn);
		   if(!$in_dir){
		       echo "get dir info fail\n";
		       return;
		   }
		    echo $in_dir."\n";
		    // 獲取當前所在ftp目錄下包含的目錄與檔案
		    $exist_dir = ftp_nlist($f_conn,".");

        //var_dump($exist_dir);

		     print_r($exist_dir);

				 if(!in_array("/mp4", $exist_dir)){
		         if(!ftp_mkdir($f_conn, "mp4")){
							   echo($exist_dir."\n");
							   echo("mp4\n");
		             echo "mkdir fail\n";

		          }else{
		              echo "mkdir mp4 success\n";
		         }
		     }

				 // 切換目錄
				 if(!ftp_chdir($f_conn, "mp4")){
				     echo "chdir fail\n";
						 ftp_close($f_conn);
				     return;
				 }else{
				     echo "chdir mp4 success\n";
				 }

				 $exist_dir = ftp_nlist($f_conn, ftp_pwd($f_conn));
 		     print_r($exist_dir);

		     // 要求是按照日期在ftp目錄下建立資料夾作為檔案上傳存放目錄
		     echo date("Y-m")."\n";
		     $dir_name = date("Y-m");
		     // 檢查ftp目錄下是否已存在當前日期的資料夾，如不存在則進行建立

		     if(!in_array($dir_name, $exist_dir)){
		         if(!ftp_mkdir($f_conn, $dir_name)){
							   echo($dir_name."\n");
		             echo "mkdir fail\n";

		          }else{
		              echo "mkdir $dir_name success\n";
		         }
		     }


         ftp_close($f_conn);
				 //$obj->play_file =  "/var/www/html/mp4/file820.mp4";
				 $cmd = 'curl -T '.$obj->play_file.' ftp://'.$user.':'.$pwd.'@'.$host.'/mp4/'.$dir_name.'/';
				 echo($cmd);

				 system($cmd);
			}

		public static function test_ftp()
		{
			$obj = new stdClass;

      $obj->id = 820;
			$dirname = date('Y-m');
			$rootdir = "/var/www/html";
			$webfilename = "/mp4";//"/mp4/".$dirname;
			$filename = $rootdir . $webfilename;

			$obj->play_file = $rootdir.$webfilename."/file".$obj->id.".mp4";


			self::ftp_upload($obj);

		}
	public static function startRecord($obj)
	{

		$items = self::getRecordById($obj->id);
		if(count($items) == 0)    return;

        $file1 = '/tmp/333';
		$file2 = '/tmp/555';
        $item = $items[0];

		$myaccs = TopHelpersUtility::getMyAccount();

		$item->timestamp = $item->timestamp-28800;
		$time = (int)$myaccs[0]->rec_sec;
		$item->timestamp = $item->timestamp-$time;
		$start = getdate($item->timestamp);
		$start_date = $start["year"]."-".$start["mon"]."-".$start["mday"];
		$start_time = $start["hours"].":".$start["minutes"].":".$start["seconds"];

		$end = getdate($item->timestamp+$time*2);
		$end_date = $end["year"]."-".$end["mon"]."-".$end["mday"];
		$end_time = $end["hours"].":".$end["minutes"].":".$end["seconds"];

		$url = 'http://admin:admin@'.$item->ipcam.'/recordlist.cgi?"starttime='.$start_date.'T'.$start_time.'Z&endtime='.$end_date.'T'.$end_time.'Z&maxcount=999"' ." -o ".$file1." -O ".$file2;

        sleep(2);
        $exec = "wget ".$url;
		system($exec);

        echo($exec);

        //return;
		$myfile = fopen($file2, "r") or die("Unable to open file!");
        $json = fread($myfile,filesize($file2));
        fclose($myfile);

		$arrs = json_decode($json);
		$id = 0;
		foreach($arrs as $i=>$arr)
		{
			foreach($arr as $i1=>$item1)
			{
				$id = $iem1->id;
				break;

			}
		}

        sleep(1);
		$dirname = date('Y-m');
        $rootdir = "/var/www/html";
		$webfilename = "/mp4/".$dirname;
		$filename = $rootdir . $webfilename;

        self::check_dir($filename);

        $webfilename = $webfilename.'/file';
		$mod_id = $obj->id%10;
		$url = 'http://admin:admin@'.$item->ipcam.'/playback.mp4?"id='.$id.'&starttime='.$start_date.'T'.$start_time.'Z&endtime='.$end_date.'T'.$end_time.'Z"' ." -o /tmp/play".$mod_id.".mp4 -O ".$rootdir.$webfilename.$obj->id.".mp4";

        $exec = "wget ".$url;

		system($exec);

		echo($exec);
		$obj->play_file = $rootdir.$webfilename.$obj->id.".mp4";

		self::ftp_upload($obj);

        //return;
		self::sendMail($obj->id,$webfilename,$item->name);

	}

		public static function create_record($obj)
		{
			$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__record_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;
      $date = date("Y-m-d");
			$time = date("H:i:s");
			$datetime = date("Y-m-d H:i:s");
			$mydate = getdate();
			$timestamp = $mydate[0];
      // Create a new query object.
      $query = $db->getQuery(true);

      // Insert columns.
      $columns = array(
            'ordering',
            'state',
            'checked_out',
            'created_by',
						'date',
						'time',
            'ipcam',
						'timestamp',
						'update',
						"name",
						'item',
						'path',
						'member',
						'title',
        );

       // Insert values.
       $values = array(
            $db->quote($ordering),
            $db->quote($state),
            $db->quote($checked_out),
            $db->quote($created_by),
						$db->quote($date),
						$db->quote($time),
						$db->quote($obj->ipcam),
            $db->quote($timestamp),
						$db->quote($datetime),
            $db->quote($obj->name),
            $db->quote($obj->type),
						$db->quote($obj->path),
						$db->quote($obj->member),
						$db->quote($obj->title),
            );

        // Prepare the insert query.
        $query
            ->insert($db->quoteName('#__record_table'))
            ->columns($db->quoteName($columns))
            ->values(implode(',', $values));

            // Set the query using our newly populated query object and execute it.
            $db->setQuery($query);
            $db->execute();
        $db->setQuery('SELECT MAX(id) FROM #__record_table');
				$max = $db->loadResult();

				return $max;

    }

    public static function waitRecord($obj)
	{
		$id = self::create_record($obj);

        $myaccs = TopHelpersUtility::getMyAccount();
		$obj->timeout = (int)$myaccs[0]->rec_sec+5;

		$cmd = '/bin/bash /opt/24dio/recordWait.sh';
		$url = 'http://127.0.0.1/index.php?option=';

		$param = '"com_floor&task=sroots.startRecord&id='.$id.'&timeout='.$obj->timeout.'"';

		$mod_id = $id%10;
        $cmd = $cmd." ".$url .' '.$param.' /tmp/record'.$mod_id.' /tmp/RECORD'.$mod_id. " ".$obj->timeout;

		system($cmd);
		echo($cmd);

	}

		public static function get_main_temp()
		{
				// Create a new query object.
				$db = JFactory::getDbo();
				$query = $db->getQuery(true);

				$query
					 ->select('DISTINCT a.*');

				$query->from('`#__device_table` AS a');

				$query->where('a.state = 1');
				$query->where('a.main = 1');
				$query->where('a.dio_type = '.self::$rs485_temp_device);

				 //JLog::add($query, JLog::INFO, 'jerror');
				 $db->setQuery($query);
		     //echo($query);
				 //JLog::add($query, JLog::INFO, 'jerror');

				 $items = (array) $db->loadObjectList();

				 return $items;


		}
		public static function get_main_elec()
		{
				// Create a new query object.
				$db = JFactory::getDbo();
				$query = $db->getQuery(true);

				$query
					 ->select('DISTINCT a.*');

				$query->from('`#__device_table` AS a');

				$query->where('a.state = 1');
				$query->where('a.main = 1');
				// $query->where('a.dio_type = '.self::$rs485_elec_device);
				// $query->where('a.dio_type IN '.$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic)));
				$query->where('a.dio_type IN ('.implode(',',
					$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic,self::$rs485_elec_devic_daepm210,self::$rs485_elec_devic_m4m,self::$rs485_elec_device_acuvim, self::$rs485_elec_device_tatung))).")");
				 //JLog::add($query, JLog::INFO, 'jerror');
				 $db->setQuery($query);
		     //echo($query);
				 //JLog::add($query, JLog::INFO, 'jerror');

				 $items = (array) $db->loadObjectList();

				 return $items;


		}

		public static function getDayHourItems($obj)
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__elec_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.year = '.$obj->year);
		 $query->where('a.month = '.$obj->month);
		 $query->where('a.day = '.$obj->day);
		 $query->where('a.type = '.self::$hour_type);

		 $query->where('a.dev_id = '.$obj->id);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;



    }

		public static function getYesterdayItems($obj)
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__elec_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.timestamp = '.$obj->timestamp);

		 $query->where('a.type = '.$obj->type);

		 $query->where('a.dev_id = '.$obj->id);

		 $db->setQuery($query);

     echo($query);
		 $items = (array) $db->loadObjectList();

		 return $items;



    }

		public static function getDayItems($obj)
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__elec_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.year = '.$obj->year);
		 $query->where('a.month = '.$obj->month);
		 $query->where('a.day = '.$obj->day);

		 if($obj->type == self::$hour_type)
     {
			 $query->where('a.hour = '.$obj->hour);

     }

		 $query->where('a.type = '.$obj->type);

		 $query->where('a.dev_id = '.$obj->id);

		 $db->setQuery($query);

     echo($query);
		 $items = (array) $db->loadObjectList();

		 return $items;



    }

		public static function getAllElec()
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__elec_table` AS a');

		 $query->where('a.state = 1');

		 $query->group('a.dev_id');

		 $db->setQuery($query);

     //echo($query);
		 $items = (array) $db->loadObjectList();

		 return $items;



    }

		public static function doUpdateDayItem($obj,$items)
    {
			$obj->hour = 0;

			$days = self::getDayItems($obj);

			if(count($days) == 0)
			{
					self::createDayItem($obj);
					$days = self::getDayItems($obj);
			}


			$day = $days[0];

      $obj->elec_kwh_cur = 0;
      $day->elec_kwh = 0;

			foreach($items as $item)
			{
					$day->elec_kwh = (float)($day->elec_kwh)+(float)($item->elec_kwh);

			    $obj->elec_kwh_cur = $item->elec_kwh_cur;

			}

      $day->elec_kwh_cur = $obj->elec_kwh_cur;

			self::updateDayItem($day);

    }

		public static function calcMonth($obj)
    {
			  $obj->type = self::$day_type;
        $items = self::getDayItems($obj);

        $obj->type = self::$month_type;
				$obj->day = 1;
				$obj->hour = 0;
        $months = self::getDayItems($obj);

        if(count($months) == 0)
				{
            self::createDayItem($obj);
            $months = self::getDayItems($obj);
				}

        $month = $months[0];

        $month->elec_kwh = 0;
				$month->elec_kwh_cur = 0;

        foreach($items as $item)
				{
					$month->elec_kwh = (float)($month->elec_kwh)+(float)($item->elec_kwh);
					$month->elec_kwh_cur = $item->elec_kwh_cur;

				}

				self::updateDayItem($month);

    }


		public static function calcYear($obj)
    {
			  $obj->type = self::$day_type;
        $items = self::getDayItems($obj);

        $obj->type = self::$year_type;
				$obj->month = 1;
				$obj->day = 1;
				$obj->hour = 0;

				$years = self::getDayItems($obj);

        if(count($years) == 0)
				{
            self::createDayItem($obj);
            $years = self::getDayItems($obj);
				}

        $year = $years[0];

				foreach($items as $item)
				{
					$year->elec_kwh = $year->elec_kwh+$item->elec_kwh;
				  $year->elec_kwh_cur = $item->elec_kwh_cur;

				}

				self::updateDayItem($year);

    }

		public static function calcDay($obj)
    {
        $items = self::getDayHourItems($obj);

        $obj->type = self::$day_type;
				self::doUpdateDayItem($obj,$items);

    }

		public static function update_acc($obj)
    {
			$db = JFactory::getDbo();

      $fields = array();

      if(isset($obj->acc_elec_kw))
			{
				$fields1 = array(

					$db->quoteName('elec_kw') . ' = ' . $db->quote($obj->acc_elec_kw),

				);
        $fields = array_merge($fields,$fields1);

      }

			if(isset($obj->elec_alarm))
			{
					 $fields1 = array(

	 					$db->quoteName('elec_alarm') . ' = ' . $db->quote($obj->elec_alarm),

	 				);
	         $fields = array_merge($fields,$fields1);
      }
			$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id),

			);
			$query = $db->getQuery(true);
			$query->update($db->quoteName('#__myaccount_table'))->set($fields)->where($conditions);

			$db->setQuery($query)->execute();

    }
		public static function update_acc_elec_kw($obj)
    {
			$obj->id = 1;

      self::update_acc($obj);

    }
		public static function update_elec_all()
    {

			  $obj = new stdClass;

				$obj->year = Date('Y');
				$obj->month = Date('m');
				$obj->day = Date('d');
				$obj->update = Date('Y-m-d H:i:s');

				$obj->hour = 0;
				$items = self::getAllElec();

				foreach($items as $item)
				{
					  $obj->id = $item->dev_id;
            self::calcDay($obj);
						self::calcMonth($obj);
						self::calcYear($obj);

        }

    }


		public static function ba_update_elec_all()
    {
			$arr = array(
				"option" => "com_floor",
				"task"   => "sroots.update_elec_all2",
			);

      $filename = "/tmp/ba_update_elec_all";
			$ips = TopHelpersUtility::getBaList();

			$obj = new stdClass;

			$obj->arr = $arr;
			$obj->filename = $filename;
			$obj->ips = $ips;

			self::sendToUrl($obj);

    }

		public static function createDayItem($obj)
		{
			$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__elec_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;

			// Create a new query object.
      $query = $db->getQuery(true);


			$a = strptime($obj->day.'-'.$obj->month.'-'.$obj->year, '%d-%m-%Y');
	    $timestamp = mktime($obj->hour, 0, 0, $a['tm_mon']+1, $a['tm_mday'], $a['tm_year']+1900);

      // Insert columns.

      $columns = array(
              'ordering',
              'state',
              'checked_out',
              'created_by',
							'year',
							'month',
							'day',
							'type',
							'dev_id',
							'elec_kwh',
							'timestamp',
							'hour',
							'update',
            );

			// Insert values.
			$values = array(
			       $db->quote($ordering),
			       $db->quote($state),
			       $db->quote($checked_out),
			       $db->quote($created_by),

						 $db->quote($obj->year),
						 $db->quote($obj->month),
						 $db->quote($obj->day),
						 $db->quote($obj->type),
						 $db->quote($obj->id),
						 $db->quote(0),
						 $db->quote($timestamp),
						 $db->quote($obj->hour),
						 $db->quote($obj->update),

			       );



      // Prepare the insert query.
      $query
            ->insert($db->quoteName('#__elec_table'))
            ->columns($db->quoteName($columns))
            ->values(implode(',', $values));

        // Set the query using our newly populated query object and execute it.
        $db->setQuery($query);
				//echo($query);
        $db->execute();

		}

		public static function update_kwh($obj)
    {
			$obj->year = Date('Y');
			$obj->month = Date('m');
			$obj->day = Date('d');
      $obj->hour = Date('H');

			$obj->type = self::$hour_type;

       $obj->update = Date('Y-m-d H:i:s');
			$hours = self::getDayItems($obj);

			if(count($hours) == 0)
			{
					self::createDayItem($obj);
					$hours = self::getDayItems($obj);
			}

			$hour = $hours[0];

      $hour->elec_kwh = $hour->elec_kwh+(float)$obj->elec_kwh;
			$hour->elec_kwh_cur = $obj->elec_kwh_cur;

      self::updateDayItem($hour);

    }


		public static function updateDayItem($obj)
    {
				$db = JFactory::getDbo();

		    $query = $db->getQuery(true)
		    ->update($db->quoteName('#__elec_table'))

		    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		    ->set($db->quoteName('elec_kwh') . ' = ' . $db->quote($obj->elec_kwh))
				->set($db->quoteName('elec_kwh_cur') . ' = ' . $db->quote($obj->elec_kwh_cur))

				->set($db->quoteName('update') . ' = ' . $db->quote($obj->update))

				->where($db->quoteName('type') . ' = ' . $db->quote($obj->type))

			  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		    $db->setQuery($query)->execute();
				//echo($query);


    }

		public static function update_elec($obj)
    {
				$update = date ("Y/m/d");
			  $update_time = date ("H:i:s");

				$db = JFactory::getDbo();
			$node_type = '';
			if ($obj->node_type == 1)
				$node_type = 'ZIGBEE插座';
			elseif ($obj->node_type == 2)
				$node_type = 'RS485單相插座';
			elseif ($obj->node_type == 3)
				$node_type = 'RS485三相電錶';

		    $query = $db->getQuery(true)
		    ->update($db->quoteName('#__device_table'))

		    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		    ->set($db->quoteName('elec_kwh') . ' = ' . $db->quote($obj->elec_kwh))
		    ->set($db->quoteName('elec_kw') . ' = ' . $db->quote($obj->elec_kw))
		    ->set($db->quoteName('elec_v') . ' = ' . $db->quote($obj->elec_v))
		    ->set($db->quoteName('elec_a') . ' = ' . $db->quote($obj->elec_a))
		    ->set($db->quoteName('elec_f') . ' = ' . $db->quote($obj->elec_f))
		    ->set($db->quoteName('elec_pf') . ' = ' . $db->quote($obj->elec_pf))
				->set($db->quoteName('update') . ' = ' . $db->quote($update))
				->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
				->set($db->quoteName('status') . ' = ' . $db->quote(1))

				->set($db->quoteName('elec_v_bn') . ' = ' . $db->quote($obj->elec_v_bn))
		    ->set($db->quoteName('elec_v_cn') . ' = ' . $db->quote($obj->elec_v_cn))
		    ->set($db->quoteName('elec_a_a') . ' = ' . $db->quote($obj->elec_a_a))
		    ->set($db->quoteName('elec_a_b') . ' = ' . $db->quote($obj->elec_a_b))
		    ->set($db->quoteName('elec_a_c') . ' = ' . $db->quote($obj->elec_a_c))
		    ->set($db->quoteName('elec_kvar') . ' = ' . $db->quote($obj->elec_kvar))
		    ->set($db->quoteName('smart_lamp_temperature1') . ' = ' . $db->quote($obj->temperature1))
		    ->set($db->quoteName('smart_lamp_temperature2') . ' = ' . $db->quote($obj->temperature2))
		    ->set($db->quoteName('smart_lamp_temperature3') . ' = ' . $db->quote($obj->temperature3))
		    ->set($db->quoteName('smart_lamp_temperature4') . ' = ' . $db->quote($obj->temperature4))
		    ->set($db->quoteName('text_value') . ' = ' . $db->quote($node_type))
		    ->set($db->quoteName('decimal_value') . ' = ' . $db->quote($obj->version))


			  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		    $db->setQuery($query)->execute();
				//echo($query);


	}
	public static function update_mitsubishielevator($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$updateQuery = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))
					->set($db->quoteName('update') . ' = ' . $db->quote($update))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('liuchuan_floor_display_name'). ' = ' . $db->quote($objectToUpdate->floor_display_name))
					// ->set($db->quoteName('liuchuan_operate_di'). ' = ' . $db->quote($objectToUpdate->operate_di))
					// ->set($db->quoteName('liuchuan_malfunction_di'). ' = ' . $db->quote($objectToUpdate->malfunction_di))
					->where($db->quoteName('id'). ' = ' . $db->quote($objectToUpdate->id));
		$db->setQuery($updateQuery)->execute();
	}
	public static function update_liuchuanelevator($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$updateQuery = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))
					->set($db->quoteName('update') . ' = ' . $db->quote($update))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('liuchuan_floor_display_name'). ' = ' . $db->quote($objectToUpdate->floor_display_name))
					->set($db->quoteName('liuchuan_operate_di'). ' = ' . $db->quote($objectToUpdate->operate_di))
					->set($db->quoteName('liuchuan_malfunction_di'). ' = ' . $db->quote($objectToUpdate->malfunction_di))
					->where($db->quoteName('id'). ' = ' . $db->quote($objectToUpdate->id));
		$db->setQuery($updateQuery)->execute();
	}
	public static function update_cosensor($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$updateQuery = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))
					->set($db->quoteName('update') . ' = ' . $db->quote($update))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('co'). ' = ' . $db->quote($objectToUpdate->co))
					// ->set($db->quoteName('status'). ' = ' . $db->quote($objectToUpdate->status))
					->where($db->quoteName('id'). ' = ' . $db->quote($objectToUpdate->id));
		$db->setQuery($updateQuery)->execute();

	}
	public static function update_analog($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$updateQuery = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))
					->set($db->quoteName('update') . ' = ' . $db->quote($update))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('text_value'). ' = ' . $db->quote($objectToUpdate->text_value))
					->set($db->quoteName('decimal_value'). ' = ' . $db->quote($objectToUpdate->decimal_value))
					// ->set($db->quoteName('status'). ' = ' . $db->quote($objectToUpdate->status))
					->where($db->quoteName('id'). ' = ' . $db->quote($objectToUpdate->id));
		$db->setQuery($updateQuery)->execute();

	}
	public static function update_jetec_soil_meter($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
					->from('#__device_table as device')
					->select('device.dio_id')
					->where('device.id =' .$db->quote($objectToUpdate->id));
		$db->setQuery($query)->execute();
		$dio_id = ((array) $db->loadObjectList())[0]->dio_id;
		// $dio_id = $db->setQuery($query)->execute()->loadObjectList()[0]->dio_id;
		// return $dio_id;
		$query = $db->getQuery(true)
					->from('#__device_table as device')
					->select('device.*')
					->where('device.dio_id = ' . $db->quote($dio_id))
					->order('device.index ASC');
		$db->setQuery($query)->execute();
		$devices = (array)$db->loadObjectList();
		$index = 1;
		foreach ($devices as $key => $device) {
			$updateQuery = $db->getQuery(true)
			->update($db->quoteName('#__device_table'))
			->set($db->quoteName('update') . ' = ' . $db->quote($update))
			->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
			->set($db->quoteName('text_value'). ' = ' . $db->quote($objectToUpdate->decimal_values[$index - 1]))
			->set($db->quoteName('decimal_value'). ' = ' . $db->quote($objectToUpdate->decimal_values[$index - 1]))
			// ->set($db->quoteName('status'). ' = ' . $db->quote($objectToUpdate->status))
			->where($db->quoteName('id'). ' = ' . $db->quote($device->id));
			$db->setQuery($updateQuery)->execute();
			$index++;
		}


	}
	public static function update_watermeter($objectToUpdate)
	{
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");
		$db = JFactory::getDbo();
		$updateQuery = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))
					->set($db->quoteName('update') . ' = ' . $db->quote($update))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('status') . ' = ' . $db->quote(1))
					->set($db->quoteName('accumulateWaterFlow'). ' = ' . $db->quote($objectToUpdate->accumulateWaterFlow))
					// ->set($db->quoteName('decimal_value'). ' = ' . $db->quote($objectToUpdate->instantWaterFlow))
					->where($db->quoteName('id'). ' = ' . $db->quote($objectToUpdate->id));
		$db->setQuery($updateQuery)->execute();
	}
	public static function add_or_update_hourly_electronicmeter_history($device_id, $accumulatepower, $year, $month, $date_of_month, $hour_of_day, $minute)
	{
		$db = JFactory::getDbo();

		$query = $db->getQuery(true)
			->select('history.*')
			->from('#__electronic_meter_history as history')
			->where('history.year = ' . $db->quote($year))
			->where('history.month = ' . $db->quote($month))
			->where('history.date_of_month = ' . $db->quote($date_of_month))
			->where('history.hour_of_day = ' . $db->quote($hour_of_day))
			->where('history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$exists = (array) $db->loadObjectList();

		$update_time = date("Y-m-d H:i:s");
		if (count($exists) > 0)
		{
			$db = JFactory::getDbo();
			if ($minute > 30)
			{
				$query = $db->getQuery(true)
					->update($db->quoteName('#__electronic_meter_history'))
					->set($db->quoteName('accumulatepower') . ' = ' . $db->quote($accumulatepower))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('last_half_hour_accmulatepower') . ' = ' . $db->quote($accumulatepower))
					->set($db->quoteName('last_half_hour_update_time') . ' = ' . $db->quote($update_time))
					->where($db->quoteName('id') . ' = ' . $db->quote($exists[0]->id));
				$db->setQuery($query);
				$db->execute();
			}
			else
			{
				$query = $db->getQuery(true)
					->update($db->quoteName('#__electronic_meter_history'))
					->set($db->quoteName('accumulatepower') . ' = ' . $db->quote($accumulatepower))
					->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
					->set($db->quoteName('first_half_hour_accmulatepower') . ' = ' . $db->quote($accumulatepower))
					->set($db->quoteName('first_half_hour_update_time') . ' = ' . $db->quote($update_time))
					->where($db->quoteName('id') . ' = ' . $db->quote($exists[0]->id));
				$db->setQuery($query);
				$db->execute();
			}
		}
		else
		{
			$lastRecord = Electronic_meter_historyHelpersElectronic_meter_history::get_last_hourly_electronic_meter_record($device_id, $year, $month,$date_of_month,$hour_of_day);
			$lastAccum = 0;
			if (count($lastRecord) > 0)
			{
				$lastAccum = $lastRecord[0]->accumulatepower;
			}
			$accumpower_column = 'accumulatepower';
			$accumpower_datetime_column = 'update_time';

			$accumpower_column1 = 'first_half_hour_accmulatepower';
			$accumpower_datetime_column1 = 'first_half_hour_update_time';
			if ($minute > 30)
			{
				$accumpower_column1 = 'last_half_hour_accmulatepower';
				$accumpower_datetime_column1 = 'last_half_hour_update_time';
			}

			$db->setQuery('SELECT MAX(ordering) FROM #__electronic_meter_history');
			$max = $db->loadResult();
			$ordering = $max+1;

			$created_by = JFactory::getUser()->name;
			$checked_out = false;
			$state = 1;
			$columns_to_insert = array(
				'ordering',
				'state',
				'checked_out',
				'created_by',
				'device_id',
				'last_accumulatepower',
				$accumpower_column,
				$accumpower_column1,
				'year',
				'month',
				'date_of_month',
				'hour_of_day',
				$accumpower_datetime_column,
				$accumpower_datetime_column1
			);
			$values_to_insert = array(
				$db->quote($ordering),
				$db->quote($state),
				$db->quote($checked_out),
				$db->quote($created_by),
				$db->quote($device_id),
				$db->quote($lastAccum),
				$db->quote($accumulatepower),
				$db->quote($accumulatepower),
				$db->quote($year),
				$db->quote($month),
				$db->quote($date_of_month),
				$db->quote($hour_of_day),
				$db->quote($update_time),
				$db->quote($update_time)
			);
			// if ($minute <= 30)
			// {
			// 	$columns_to_insertcolumns_to_insert = array_merge($columns_to_insert, ['first_half_hour_accmulatepower', 'first_half_hour_update_time' ]);
			// 	$values_to_insert = array_merge($values_to_insert, [$db->quote($accumulatepower), $db->quote(date("Y-m-d H:i:s"))]);
			// }
			$query = $db->getQuery(true)
						->insert($db->quoteName('#__electronic_meter_history'))
						->columns($db->quoteName($columns_to_insert))
						->values(implode(',', $values_to_insert));
						// ->set($db->quoteName);
			$db->setQuery($query);
			$db->execute();
		}

	}
	public static function add_or_update_hourly_watermeter_history($device_id, $accumulateWaterFlow, $year, $month, $date_of_month, $hour_of_day)
	{
		$db = JFactory::getDbo();

		$query = $db->getQuery(true)
			->select('history.*')
			->from('#__water_meter_history as history')
			->where('history.year = ' . $db->quote($year))
			->where('history.month = ' . $db->quote($month))
			->where('history.date_of_month = ' . $db->quote($date_of_month))
			->where('history.hour_of_day = ' . $db->quote($hour_of_day))
			->where('history.device_id = ' . $db->quote($device_id));
		$db->setQuery($query);
		$exists = (array) $db->loadObjectList();
		if (count($exists) > 0)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true)
				->update($db->quoteName('#__water_meter_history'))
				->set($db->quoteName('accumulateWaterFlow') . ' = ' . $db->quote($accumulateWaterFlow))
				->set($db->quoteName('update_time') . ' = ' . $db->quote(date("Y-m-d H:i:s")))
				->where($db->quoteName('id') . ' = ' . $db->quote($exists[0]->id));
			$db->setQuery($query);
			$db->execute();
		}
		else
		{
			$lastRecord = Water_meter_historyHelpersWater_meter_history::get_last_hourly_water_meter_record($device_id, $year, $month,$date_of_month,$hour_of_day);
			$lastAccum = 10;
			if (count($lastRecord) > 0)
			{
				$lastAccum = $lastRecord[0]->accumulatewaterflow;
			}
			// $lastAccum = count($lastRecord);
			$db->setQuery('SELECT MAX(ordering) FROM #__water_meter_history');
			$max = $db->loadResult();
			$ordering = $max+1;

			$created_by = JFactory::getUser()->name;
			$checked_out = false;
			$state = 1;

			$columns_to_insert = array(
				'ordering',
				'state',
				'checked_out',
				'created_by',
				'device_id',
				'accumulateWaterFlow',
				'last_accumulatewaterflow',
				'year',
				'month',
				'date_of_month',
				'hour_of_day',
				'update_time');
			$values_to_insert = array(
				$db->quote($ordering),
				$db->quote($state),
				$db->quote($checked_out),
				$db->quote($created_by),
				$db->quote($device_id),
				$db->quote($accumulateWaterFlow),
				$db->quote($lastAccum),
				$db->quote($year),
				$db->quote($month),
				$db->quote($date_of_month),
				$db->quote($hour_of_day),
				$db->quote(date("Y-m-d H:i:s"))
			);
			$query = $db->getQuery(true)
						->insert($db->quoteName('#__water_meter_history'))
						->columns($db->quoteName($columns_to_insert))
						->values(implode(',', $values_to_insert));
						// ->set($db->quoteName);
			$db->setQuery($query);
			$db->execute();
		}

	}
	public static function add_hourly_watermeter_history($device_id, $accumulateWaterFlow, $year, $month, $date_of_month, $hour_of_day)
	{
		$db = JFactory::getDbo();
		$db->setQuery('SELECT MAX(ordering) FROM #__water_meter_history');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
		$state = 1;

		$columns_to_insert = array('ordering','state','checked_out','created_by','device_id', 'accumulateWaterFlow', 'year', 'month', 'date_of_month', 'hour_of_day', 'update_time');
		$values_to_insert = array(
			$db->quote($ordering),
			$db->quote($state),
			$db->quote($checked_out),
			$db->quote($created_by),
			$db->quote($device_id),
			$db->quote($accumulateWaterFlow),
			$db->quote($year),
			$db->quote($month),
			$db->quote($date_of_month),
			$db->quote($hour_of_day),
			$db->quote(date("Y-m-d H:i:s"))
		);
		$query = $db->getQuery(true)
					->insert($db->quoteName('#__water_meter_history'))
					->columns($db->quoteName($columns_to_insert))
					->values(implode(',', $values_to_insert));
					// ->set($db->quoteName);
		$db->setQuery($query);
		$db->execute();
	}

	public static function chceck_if_hourly_watermeter_record_exists($device_id, $year, $month, $date_of_month,$hour_of_day)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
					->select('id')
					->from('#__water_meter_history as water_meter_history')
					->order($db->escape('id DESC'))
					->where('water_meter_history.device_id = ' . $db->quote($device_id))
					->where('water_meter_history.year = ' . $db->quote($year))
					->where('water_meter_history.month = ' . $db->quote($month))
					->where('water_meter_history.date_of_month = ' . $db->quote($date_of_month))
					->where('water_meter_history.hour_of_day = ' . $db->quote($hour_of_day))
					->setLimit(1);
		$db->setQuery($query);
		return count((array) $db->loadObjectList()) > 0;
	}
	public static function update_iaq($obj)
    {
		$update = date ("Y/m/d");
		$update_time = date ("H:i:s");

		$db = JFactory::getDbo();

		$query = $db->getQuery(true)
			->update($db->quoteName('#__device_table'))
			->set($db->quoteName('temp') . ' = ' . $db->quote($obj->temperature))
			->set($db->quoteName('humidity') . ' = ' . $db->quote($obj->humidity))
			->set($db->quoteName('co2_ppm') . ' = ' . $db->quote($obj->co2))
			->set($db->quoteName('co') . ' = ' . $db->quote($obj->co))
			->set($db->quoteName('hcho') . ' = ' . $db->quote($obj->hcho))
			->set($db->quoteName('tvoc') . ' = ' . $db->quote($obj->tvoc))
			->set($db->quoteName('pm01') . ' = ' . $db->quote($obj->pm01))
			->set($db->quoteName('pm25') . ' = ' . $db->quote($obj->pm25))
			->set($db->quoteName('pm10') . ' = ' . $db->quote($obj->pm10))
			->set($db->quoteName('update') . ' = ' . $db->quote($update))
			->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
			->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		$db->setQuery($query)->execute();
	}
		public static function update_temp($obj)
    {
				$update = date ("Y/m/d");
			  $update_time = date ("H:i:s");

				$db = JFactory::getDbo();

		    $query = $db->getQuery(true)
		    ->update($db->quoteName('#__device_table'))

		    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		    ->set($db->quoteName('temp') . ' = ' . $db->quote($obj->temp))
				->set($db->quoteName('humidity') . ' = ' . $db->quote($obj->humidity))
				->set($db->quoteName('lux') . ' = ' . $db->quote($obj->lux))
				->set($db->quoteName('co2_ppm') . ' = ' . $db->quote($obj->co2_ppm))
				->set($db->quoteName('co') . ' = ' . $db->quote($obj->co))
				->set($db->quoteName('decimal_value') . ' = ' . $db->quote($obj->decimal_value))
				->set($db->quoteName('update') . ' = ' . $db->quote($update))
				->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

			  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		    $db->setQuery($query)->execute();


		}
		public static function add_smart_lamp_log($obj)
    {
				$update = date ("Y/m/d");
				$update_time = date ("H:i:s");

				$update_time = new DateTime();
				$update_time->setTimestamp($obj->timestamp);
				$update_time_string = $update_time->format("Y/m/d H:i:s");

				$db = JFactory::getDbo();

		    $query = $db->getQuery(true)
		    ->insert($db->quoteName('#__smart_lamp_history'))

		    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		    ->set($db->quoteName('voltage') . ' = ' . $db->quote($obj->voltage))
				->set($db->quoteName('current') . ' = ' . $db->quote($obj->current))
				->set($db->quoteName('power') . ' = ' . $db->quote($obj->power))
				->set($db->quoteName('temperature1') . ' = ' . $db->quote($obj->temperature1))
				->set($db->quoteName('temperature2') . ' = ' . $db->quote($obj->temperature2))
				->set($db->quoteName('temperature3') . ' = ' . $db->quote($obj->temperature3))
				->set($db->quoteName('temperature4') . ' = ' . $db->quote($obj->temperature4))
				// ->set($db->quoteName('status') . ' = ' . $db->quote($obj->status))
				->set($db->quoteName('accumulate_power') . ' = ' . $db->quote($obj->accumulatePower))
				// ->set($db->quoteName('update') . ' = ' . $db->quote($update))
				->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time_string))

			  ->set($db->quoteName('device_id') . ' = ' . $db->quote($obj->id));

		    $db->setQuery($query)->execute();


    }
		public static function update_smart_lamp($obj)
    {
				$update = date ("Y/m/d");
			  $update_time = date ("H:i:s");

				$db = JFactory::getDbo();

		    $query = $db->getQuery(true)
		    ->update($db->quoteName('#__device_table'))

		    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
		    ->set($db->quoteName('smart_lamp_voltage') . ' = ' . $db->quote($obj->voltage))
				->set($db->quoteName('smart_lamp_current') . ' = ' . $db->quote($obj->current))
				->set($db->quoteName('smart_lamp_power') . ' = ' . $db->quote($obj->power))
				->set($db->quoteName('smart_lamp_temperature1') . ' = ' . $db->quote($obj->temperature1))
				->set($db->quoteName('smart_lamp_temperature2') . ' = ' . $db->quote($obj->temperature2))
				->set($db->quoteName('smart_lamp_temperature3') . ' = ' . $db->quote($obj->temperature3))
				->set($db->quoteName('smart_lamp_temperature4') . ' = ' . $db->quote($obj->temperature4))
				->set($db->quoteName('status') . ' = ' . $db->quote($obj->status))
				->set($db->quoteName('smart_lamp_accumulate_power') . ' = ' . $db->quote($obj->accumulatePower))
				->set($db->quoteName('update') . ' = ' . $db->quote($update))
				->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

			  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

		    $db->setQuery($query)->execute();


    }
		public static function del_device($devices,$count)
		{
	            //JLog::add('id=' . $obj->id, JLog::INFO);
        $arr = array();
				foreach($devices as $i=>$item)
				{
				    if($i >= $count)
						{
							array_push($arr,$item);
						}
				}

				foreach($arr as $i=>$item)
				{
					self::delete_device($item->id);
				}
		}

		public static function check_item($obj)
		{
			$items = self::get_all_devices($obj->myid,$obj->group);

      $need_reload = false;
			if(count($items) > $obj->item)
			{
          self::del_device($items,$obj->item);
					$need_reload = true;
			}
			else if(count($items) < $obj->item)
			{
					self::add_device($obj,count($items));
					$need_reload = true;
					//self::add_point1($obj->myid);
			}

			if($need_reload)
			{
				TopHelpersUtility::send_reload_center();
			}

    }

		public static function getThisGroupItem($mygroup)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');

			 $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

    public static function getGroupItem($mygroup)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$page);

       if($mygroup > 0)
			     $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getSubGroupItem($groupid)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$tdiopage);

       $query->where('a.type = '.$groupid);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getSuperGroupItem($mygroup)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$tdpage);

       if($mygroup > 0)
			     $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getCCTVDev()
		{
			$items = TopHelpersUtility::getCCTVTop();

			foreach($items as $i=>$item)
			{
				$item->IPs = self::getIPDevices($item->id,0,true);
			}

			return $items;

		}

		public static function getOfflineExpireGroupItem()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$page);
			 $query->where('a.offline_cnt = 0');
			 $query->where('a.type = 2 OR a.type = 3');


		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function getOnlineExpireGroupItem()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$page);
			 $query->where('a.online_cnt = 0');
			 $query->where('a.type = 2 OR a.type = 3');


		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function getIPGroupItem($mygroup)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$page);
			 $query->where('a.type = 2 OR a.type = 3');

       if($mygroup > 0)
			     $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function getBuildingItem($mybuilding)
		{
			  //JLog::add($mybuilding, JLog::INFO, 'jerror');

			  $str_secs = explode("-",$mybuilding);

			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$bpage);

       $is_remove = false;
			 $rest = substr($str_secs[0], 0, 1);
			 if($rest == "D")
			 {
				 $is_remove = true;
				 $str_secs[0] = str_replace('D', '', $str_secs[0]);
			 }

       if($str_secs[0] > 0)
			 {
				   $str = "";

				   foreach($str_secs as $i=>$item)
					 {
						   if($i > 0)
							 {
								 if($is_remove)
								     $str = $str ." AND ";
								 else
								     $str = $str ." OR ";
							 }

               if($is_remove)
							     $str = $str . ' a.id != '.$item;
							 else
                   $str = $str . ' a.id = '.$item;

					 }

			     //$query->where('a.id = '.$mybuilding);
					 $query->where($str);
       }

		   $db->setQuery($query);

       //JLog::add($query, JLog::INFO, 'jerror');
		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function get_all_devices($id,$group=0,$is_enable=0)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.floor = ' . $id);

     if($group > 0)
		     $query->where('a.group = ' . $group);

		 if($is_enable == 1)
				  $query->where('a.enable = 1');

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();
		 $ids = [];
		 for ($i=0; $i < count($items); $i++) {
			// $items[$i]->note = $items[$i]->note . $items[$i]->dio_type . $items[$i]->info;
			if ($items[$i]->dio_type == 999)
			{
				$clone_target_id = (int)str_replace("C$", "", $items[$i]->info);
				if (is_numeric($clone_target_id))
				{
					array_push($ids,$clone_target_id);
				}
			}
		 }

		//  if ($group != 0)
			//  $cloned_devices = array_filter($devices,function($device){
			// 	return $device->dio_type == 999;
			//  });
			// $cloned_devices = array_filter($items,function($device){
			// 	return $device->dio_type == 999;
			// });
			// for ($i=0; $i < count($items); $i++) {
			// 	# code...
			// }
			// $db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');
		 $all_devices = [];
		 if (count($ids))
		 {
			 $query->where('a.id in ('.implode(',',$ids).')');
			 $db->setQuery($query);

			 $all_devices = (array) $db->loadObjectList();
		 }

		//  $query->where('a.floor = ' . $id);

		// $query->where('a.enable = 1');


		 if (count($all_devices))
		 {
			for ($i=0; $i < count($items); $i++) {
				// $items[$i]->note = $items[$i]->note . $items[$i]->dio_type . $items[$i]->info;
				if ($items[$i]->dio_type == 999)
				{
					// $items[$i]->note = $device->dio_type;
					$clone_target_id = (int)str_replace("C$", "", $items[$i]->info);
					if (is_numeric($clone_target_id))
					{
						// $items[$i]->note = $items[$i]->note . $items[$i]->dio_type . $clone_target_id;
						// $items[$i]->note =  "9991".$clone_target_id;
						for ($j=0; $j < count($all_devices); $j++)  {
							if ($all_devices[$j]->id == $clone_target_id)
							{
								$temp = $items[$i];

								$items[$i] = $all_devices[$j];
								// $items[$i]->id = $temp->id;
								$items[$i]->pointx = $temp->pointx;
								$items[$i]->pointy = $temp->pointy;
								$items[$i]->width = $temp->width;
								$items[$i]->height = $temp->height;
								$items[$i]->note = $temp->note;
								// $items[$i]->info = $temp->info;
								// $items[$i]->update = $temp->update;
								// $items[$i]->update_time = $temp->update_time;
								$items[$i]->red = $temp->red;
								$items[$i]->green = $temp->green;
								$items[$i]->yellow = $temp->yellow;
								$items[$i]->fontsize = $temp->fontsize;
								$items[$i]->show_node_text = $temp->show_node_text;
								// $items[$i]->id = $all_devices[$j]->id;
								// $items[$i]->dio_type = $all_devices[$j]->dio_type;
								// $items[$i]->status = $all_devices[$j]->status;
								// $items[$i]->note = $all_devices[$j]->status;
							}
						}

					}
					// else if (strpos($items[$i]->info, "E$"))
					// {
					// 	$power_meter_ids = explode(",", str_replace("E$","",$items[$i]->info));

					// 	$items[$i]->elec_kw = 0;
					// 	$items[$i]->power_meter_ids = $power_meter_ids;
					// 	for ($j=0; $j < count($all_devices); $j++)  {
					// 		if (in_array($power_meter_ids,$all_devices[$j]->id))
					// 		{
					// 			$items[$i]->elec_kw += $all_devices[$j]->elec_kw;
					// 		}
					// 	}
					// }

				}
			}
		 }

		 $device_brands = FloorHelpersFloor::getDeviceBrands();
		 for ($i=0; $i < count($items); $i++) {

			 $items[$i]->web_view_subpath = "/";
			 $items[$i]->web_view_port = "80";
			 $items[$i]->web_view_scheme = "http";
			 if ($items[$i]->device_brand_id !=0) {
				for ($j=0; $j < count($device_brands); $j++) {
					// $device_brand = $device_brands[$j];
					if ($device_brands[$j]->id == $items[$i]->device_brand_id) {
						$items[$i]->web_view_subpath = $device_brands[$j]->web_view_subpath;
						$items[$i]->web_view_port = $device_brands[$j]->web_view_port;
						$items[$i]->web_view_scheme = $device_brands[$j]->web_view_scheme;
					}

				}
			 }
		 }
		 return $items;
		}

		public static function get_elec_devices($id,$group=0,$is_enable=0)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.floor = ' . $id);

		//  $query->where('a.dio_type = ' . self::$rs485_elec_device);
		//  $query->where('a.dio_type'.' IN '.$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic)));
		 $query->where('a.dio_type IN ('.implode(',',$db->quote(array(self::$rs485_elec_device,self::$rs485_elec_device_cic,self::$rs485_elec_devic_daepm210,self::$rs485_elec_device_acuvim,self::$rs485_elec_device_tatung))).")");

     if($group > 0)
		     $query->where('a.group = ' . $group);

		 if($is_enable == 1)
				  $query->where('a.enable = 1');

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
		}

		public static function get_floor_device_items()
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.*');

	     $query->from('`#__device_table` AS a');

	     $query->where('a.state = 1');

			 $query->group('a.floor');

	     $query->order('a.id');

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }

		public static function get_all_device_state($myid)
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.id, a.status, a.number');

	     $query->from('`#__device_table` AS a');

	     $query->where('a.state = 1');

			 $query->where('a.floor = ' . $myid);

	     $query->order('a.id');

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }

		public static function get_floot_point_items()
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.*');

	     $query->from('`#__device_table` AS a');

	     $query->where('a.state = 1');

			 $query->group('a.floor');

	     $query->order('a.id');

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }

		public static function adjust_points($points)
		{

			foreach($points as $i => $item)
			{
				if($item->pointx == -1)
				{
					$item->pointx = 0;
					$item->pointy = 0;
				}

        if(empty($item->green))
				{
					$mygroups = self::getGroupItem($item->group);
					if(count($mygroups))
					{
						$item->green = $mygroups[0]->green;
						$item->red = $mygroups[0]->red;
						$item->yellow = $mygroups[0]->yellow;
					}
				}
			}

			return $points;
    }

		public static function get_all_building_floor($building_id)
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.*');

	     $query->from('`#__floor_table` AS a');

       $query->where('a.state = 1');

       if($building_id > 0)
	         $query->where('a.building = ' . $building_id);

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }

		public static function get_all_floor($group_id)
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.*');

	     $query->from('`#__floor_table` AS a');

       $query->where('a.state = 1');

       if($group_id > 0)
	         $query->where('a.group = ' . $group_id);

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }

		public static function get_my_floor($id)
		{
	      // Create a new query object.
	      $db = JFactory::getDbo();
	      $query = $db->getQuery(true);

	      // Select the required fields from the table.
	     $query
       ->select('DISTINCT a.*');

	     $query->from('`#__floor_table` AS a');


	     $query->where('a.id = ' . $id);

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

       return $items;

    }



		public static function do_add_device($obj,$index)
		{

			$floor = $obj->myid;
			$group = $obj->group;
      $width = $obj->height;
			$height = $obj->height;

			$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__device_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;
	    $enable = 1;
			$fontsize = 3;
			$color = "black";

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

      // Create a new query object.
      $query = $db->getQuery(true);

      // Insert columns.
            $columns = array(
                      'ordering',
                      'state',
                      'checked_out',
                      'created_by',
                      'floor',
                      'index',
                      'pointx',
                      'status',
											'group',
											'width',
											'height',
											'type',
											'enable',
											'fontsize',
											'color',
											'update',
											'update_time',
											'dio_id',
                      );

            // Insert values.
            $values = array(
                      $db->quote($ordering),
                      $db->quote($state),
                      $db->quote($checked_out),
                      $db->quote($created_by),
                      $db->quote($floor),
                      $db->quote($index),
                      $db->quote(-1),
                      $db->quote(1),
											$db->quote($group),
											$db->quote($width),
											$db->quote($height),
											$db->quote($obj->type),
											$db->quote($enable),
											$db->quote($fontsize),
											$db->quote($color),
											$db->quote($update),
											$db->quote($update_time),
											$db->quote(0),

                      );

            // Prepare the insert query.
            $query
                ->insert($db->quoteName('#__device_table'))
                ->columns($db->quoteName($columns))
                ->values(implode(',', $values));

                // Set the query using our newly populated query object and execute it.
                $db->setQuery($query);
                $db->execute();

    }

		public static function add_device($obj,$item1)
		{
			 $max1 = $obj->item;

        for($i=$item1;$i<$max1;$i++)
				{
				    self::do_add_device($obj,$i);
				}

		}

		public static function reset_dio_id()
	  {
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__device_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('dio_id') . ' = ' . $db->quote((int)0))
			->set($db->quoteName('update') . ' = ' . $db->quote($update))
			->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

		  ->where($db->quoteName('id') . ' < ' . $db->quote(122));

	    $db->setQuery($query)->execute();

	  }
	  	public static function change_old_device_addr($obj)
		{
			self::change_old_device_addr($obj, true);
		}
		public static function change_old_device_addr_with_type($obj, $with_type)
	  {
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__device_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('pointx') . ' = ' . $db->quote((int)$obj->pointx))
	    ->set($db->quoteName('pointy') . ' = ' . $db->quote((int)$obj->pointy))
			->set($db->quoteName('info') . ' = ' . $db->quote($obj->info))
			->set($db->quoteName('note') . ' = ' . $db->quote($obj->note))
			->set($db->quoteName('width') . ' = ' . $db->quote($obj->width))
			->set($db->quoteName('height') . ' = ' . $db->quote($obj->height))
			->set($db->quoteName('enable') . ' = ' . $db->quote($obj->enable))
			->set($db->quoteName('fontsize') . ' = ' . $db->quote($obj->fontsize))
			->set($db->quoteName('device_brand_id') . ' = ' . $db->quote($obj->device_brand_id))
			->set($db->quoteName('color') . ' = ' . $db->quote($obj->color))
			->set($db->quoteName('update') . ' = ' . $db->quote($update))
			->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

		  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id1));
		  if ($with_type)
		  {
			  $query = $query->set($db->quoteName('type') . ' = ' . $db->quote($obj->type));
		  }

      if($obj->type == 3)
			{
				$query->set($db->quoteName('path') . ' = ' . $db->quote($obj->info));
			}
	    $db->setQuery($query)->execute();

	  }

    public static function ping_check($items,$is_status)
		{
			$c = array();
			$d = array();
			$a = array();

      if($is_status == 1)
			{
				$mymatch = "unreachable";
				$match_status = 3;
			}
			else
			{
				$mymatch = "alive";
				$match_status = 1;
			}
      $pingFile = "/tmp/ping" . $is_status . ".txt";
			$myfile = fopen($pingFile, "w");

			foreach($items as $i=>$item)
			{
				//array_push($a,$item);
				//array_push($c,$item);
				$a[$item->id] = $item->info;
				$c[$item->id] = $item->status;
				$d[$item->id] = $is_status;
				$txt = $item->info . "\n";

				fwrite($myfile, $txt);          //$a = array_merge($a,$b);
			}

      fclose($myfile);

      system("sudo fping -f " . $pingFile . "> /tmp/ping".$is_status);

			$myfile = fopen("/tmp/ping".$is_status, "r") or die("Unable to open file!");
      // Output one line until end-of-file

      while(!feof($myfile)) {
          $txt = fgets($myfile);
					$ret = strpos($txt,$mymatch);

					if($ret !== false)
					{
						foreach($a as $i=>$item)
						{
							echo $item."</br>";
							$myret = strpos($txt,$item);

							if($myret !== false)
							{
								$d[$i] = $match_status;
                //echo("<br>".$item." ".$match_status."<br>");
							}


						}
					}
      }
      fclose($myfile);

      $online_item = array();
			$offline_item = array();
			foreach($c as $i=>$item)
			{
				if($item != $d[$i])
				{
					foreach($items as $i1=>$item1)
					{
						  if($item1->id == $i)
							{
								   $item1->status = $d[$i];
					         self::update_device_state($item1);
									 $item1->type = "IP";
									 $item1->need_alarm = 1;
									 self::add_to_devicelog($item1);
									 if($item1->status == 1)
									 {
										 array_push($online_item, $item1);
									 }
									 else if($item1->status == 3)
									 {
										 array_push($offline_item, $item1);
									 }

									 break;
						  }
					}
				}
			}

			self::sendMessage($online_item);
			self::sendMessage($offline_item);


		}

		public static function getIPStatus($is_status)
		{
			//return false;
			$items = self::getIPDevices1($is_status);

      if(count($items) == 0) return false;


      //echo('getIPStatus');
			$num_arr = array();
			foreach($items as $i => $item)
			{

					if($item->pointx >= 0 && !empty($item->info) && $item->info != null)
					{
						  echo($item->info."<br>");
				      array_push($num_arr, $item);
          }

	        if(count($num_arr) >= 50)
					{
						  //echo("num 2<br>");
	            self::ping_check($num_arr,$is_status);
							$num_arr = array();
					}

			}

			if(count($num_arr) > 0)
			{
					//echo("num 2<br>");
					self::ping_check($num_arr,$is_status);
					//$num_arr = array();
			}

      return true;
   }

		public static function resetOnlineGroup($item)
		{
			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__top_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('online_cnt') . ' = ' . $db->quote($item->online))
	    ->where($db->quoteName('id') . ' = ' . $db->quote($item->id));

	    $db->setQuery($query)->execute();

		}

		public static function resetOfflineGroup($item)
		{
			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__top_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('offline_cnt') . ' = ' . $db->quote($item->offline))
	    ->where($db->quoteName('id') . ' = ' . $db->quote($item->id));

	    $db->setQuery($query)->execute();

		}
		public static function getFireAlarmDevicesData() {
			return self::getFireAlarmDevices();
		}
		public static function getFireAlarmFloorsData() {
			return self::getFireAlarmFloors();
		}
		public static function getAllBuildingAlarmCount($except_device_ids) {
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('count(device.id) as alarm_count, floor.building')
					->join('LEFT', '#__floor_table AS floor ON floor.id = device.floor')
					->where('device.state = 1')
					->where('device.enable = 1')
					->where('`status` = 3')
					->where('trigger_alarm = 1');
			if (count($except_device_ids) > 0)
			{
				$query->where('device.id not in ('. implode(',', $except_device_ids). ')');
			}
			$query->group('building');
			$query->where('building is not null');

			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getAllMenuAlarmCount() {

			$db = JFactory::getDbo();
			// $sql = "SELECT id,title,alias,`PATH`,link,replace(SUBSTRING_INDEX(SUBSTRING_INDEX(link,'filter_building=',-1),'&',1),'D','') AS buildings  from `#__menu` WHERE link LIKE '%filter_building=%'";
			$sql = "SELECT id, link, LEFT(SUBSTRING_INDEX(SUBSTRING_INDEX(link,'filter_building=',-1),'&',1),1) ='D' AS reversed, replace(SUBSTRING_INDEX(SUBSTRING_INDEX(link,'filter_building=',-1),'&',1),'D','') AS buildings  from xwzms_menu WHERE link LIKE '%filter_building=%'";
			$db->setQuery($sql);
			$result = (array)$db->loadObjectList();
			foreach ($result as $key => $value) {
				$result[$key]->buildings = explode('-',$value->buildings);
			}
			return $result;
		}
		public static function getAllFloorAlarmCount($except_device_ids) {
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('device.floor, count(device.id) as alarm_count')
					->join('LEFT', '#__floor_table AS floor ON floor.id = device.floor')
					->where('device.state = 1')
					->where('device.enable = 1')
					->where('`status` = 3')
					->where('trigger_alarm = 1');
			if (count($except_device_ids) > 0)
			{
				$query->where('device.id not in ('. implode(',', $except_device_ids). ')');
			}
			$query->group('floor');

			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		// public static function getAllBuildingAlarmCount() {
		// 	$db = JFactory::getDbo();
		// 	$query = $db->getQuery(true);
		// 	$query->from('`#__device_table` AS device')
		// 			->select('device.floor, count(device.id) as alarm_count')
		// 			->where('device.state = 1')
		// 			->where('device.enable = 1')
		// 			->where('`status` = 3')
		// 			->where('trigger_alarm = 1')
		// 			->group('floor');

		// 	$db->setQuery($query);
		// 	return (array) $db->loadObjectList();
		// }
		public static function getAllEnableDeviceDataByFloor($floor_id) {
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.state = 1')
					->where('device.enable = 1')
					->where('device.floor = '. $db->quote($floor_id));
			$db->setQuery($query);
			$result = (array) $db->loadObjectList();
			// $result = (array) $db->loadObjectList();


			// $db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.state = 1')
					->where('device.enable = 1')
					->where('device.dio_type = 999')
					->where("device.info like 'E$%'")
					->where('device.floor = '. $db->quote($floor_id));
			$db->setQuery($query);
			$meter_comb_devices = (array) $db->loadObjectList();

			$power_meter_ids = [];
			foreach ($meter_comb_devices as $key => $item) {
					$expect_meter_ids = explode(",", str_replace("E$","",$item->info));

					$power_meter_ids = array_merge($power_meter_ids, $expect_meter_ids);

			}
			if (count($power_meter_ids) > 0)
			{
				$unsigned_power_meter_ids = [];
				foreach ($power_meter_ids as $key => $power_meter_id) {
					array_push($unsigned_power_meter_ids, abs($power_meter_id));
				}
				$db = JFactory::getDbo();
				$query = $db->getQuery(true);
				$query->from('`#__device_table` AS device')
						->select('DISTINCT device.*')
						->where('device.state = 1')
						->where('device.id in ('. implode(',',$unsigned_power_meter_ids).')');
				$db->setQuery($query);
				$power_meters = (array) $db->loadObjectList();
				try {
					foreach ($result as $key => $item) {
						if ($item->dio_type == 999 && strpos($item->info, "E$")> -1) {
							$expect_meter_ids = explode(",", str_replace("E$","",$item->info));
							$unsigned_expect_meter_ids = [];

							foreach ($expect_meter_ids as $key1 => $expect_meter_id) {
								$nt = new StdClass();
								$nt->id  = abs($expect_meter_id);
								if ($expect_meter_id < 0)
								{
									$nt->negative = true;
								}
								else
								{
									$nt->negative = false;
								}
								array_push($unsigned_expect_meter_ids, $nt);
							}
							foreach ($power_meters as $key1 => $power_meter) {
								foreach ($unsigned_expect_meter_ids as $key2 => $unsigned_expect_meter_id) {
									if ($power_meter->id == $unsigned_expect_meter_id->id)
									{
										if ($unsigned_expect_meter_id->negative)
										{
											$result[$key]->elec_kw -= $power_meter->elec_kw;
										}
										else
										{
											$result[$key]->elec_kw += $power_meter->elec_kw;
										}
									}
								}
							}

							$result[$key]->elec_kw = round($result[$key]->elec_kw,2);
						}
					}
				} catch (\Throwable $th) {
					//throw $th;
				}
			}

			return $result;
		}
		public static function getTotalSoyalPowerForHome() {

			$yesterday = new DateTime('yesterday',new DateTimeZone('Asia/Taipei'));
			$day_before_yesterday = new DateTime('yesterday',new DateTimeZone('Asia/Taipei'));
			$day_before_yesterday->modify('-1 day');
			$day_before_yesterday =
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from("`#__device_table` AS device")
					->where('device.state = 1')
					->where('device.enable = 1')
					->where('device.dio_type IN(19,33,37)')
					->where('device.main = 1')
					->select("DISTINCT device.*");
			$db->setQuery($query);
			$solar_power_devices = (array) $db->loadObjectList();
			$solar_pwoer_ids = array_column($solar_power_devices, 'id');
			// return $solar_pwoer_ids;

			$years = $db->quote(implode(',',array(
				$yesterday->format('Y'),
				$day_before_yesterday->format('Y')
			)));
			$months = $db->quote(implode(',',array(
				$yesterday->format('m'),
				$day_before_yesterday->format('m')
			)));
			$days = $db->quote(implode(',',array(
				$yesterday->format('d'),
				$day_before_yesterday->format('d')
			)));
			// return $days;
			// return "AAAAAAAAAAAAAAAAAA";
			$query = $db->getQuery(true);
			$query->from("`#__elec_table` AS elec")
					->select('DISTINCT elec.*')
					->where('elec.state = 1')
					->where('elec.enable = 1')
					->where('elec.main = 1')
					->where('elec.type = 6')
					->where('elec.year IN(' . $years .')')
					->where('elec.month IN(' . $months .')');
					// ->where('elec.day =26');
					// ->where('elec.month IN(' . implode(',',$db->quote($yesterday->format('m'),$db->quote($day_before_yesterday->format('m'))))
					// ->where('elec.day IN(' . implode(',',$db->quote($yesterday->format('d'),$db->quote($day_before_yesterday->format('d')))))
					// ->where('elec.dev_id IN('.implode(',',$db->quote($solar_pwoer_ids)))
					// ->order('elec.dev_id ASC, elec.timestamp DESC');
			$db->setQuery($query);
			$elec_histories = (array) $db->loadObjectList();
			// return (array) $db->loadObjectList();
			return $elec_histories;
		}
		public static function getPmSensorData() {
			$pmSensorDevices = self::getPmSensorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($pmSensorDevices); $i++)
			{
			// foreach($coSensorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->pm01 = $pmSensorDevices[$i]->pm01;
				$item->pm25 = $pmSensorDevices[$i]->pm25;
				$item->pm10 = $pmSensorDevices[$i]->pm10;
				$item->dio_type = $pmSensorDevices[$i]->dio_type;
				$item->id = $pmSensorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getCoSensorData() {
			$coSensorDevices = self::getCoSensorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($coSensorDevices); $i++)
			{
			// foreach($coSensorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->co = $coSensorDevices[$i]->co;
				$item->id = $coSensorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getLiuchuanElevatorData() {
			$liuchuanElevatorDevices = self::getLiuchuanElevatorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($liuchuanElevatorDevices); $i++)
			{
			// foreach($liuchuanElevatorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->liuchuan_floor_display_name = $liuchuanElevatorDevices[$i]->liuchuan_floor_display_name;
				$item->id = $liuchuanElevatorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getMitsubishiElevatorData() {
			$mitsubishiElevatorDevices = self::getMitsubishiElevatorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($mitsubishiElevatorDevices); $i++)
			{
			// foreach($mitsubishiElevatorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->mitsubishi_floor_display_name = $mitsubishiElevatorDevices[$i]->liuchuan_floor_display_name;
				$item->id = $mitsubishiElevatorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getIrtiIvaPersonDetectionData() {
			$irtiIvaPersonDetectionDevices = self::getIrtiIvaPersonDetectionDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($irtiIvaPersonDetectionDevices); $i++)
			{
			// foreach($irtiIvaPersonDetectionDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->decimal_value = round($irtiIvaPersonDetectionDevices[$i]->decimal_value);
				$item->id = $irtiIvaPersonDetectionDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getIrtiIvaPersonCountingData() {
			$irtiIvaPersonCountingDevices = self::getIrtiIvaPersonCountingDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($irtiIvaPersonCountingDevices); $i++)
			{
			// foreach($irtiIvaPersonCountingDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->decimal_value = round($irtiIvaPersonCountingDevices[$i]->decimal_value);
				$item->id = $irtiIvaPersonCountingDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getFujiElevatorData() {
			$fujiElevatorDevices = self::getFujiElevatorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($fujiElevatorDevices); $i++)
			{
			// foreach($fujiElevatorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->floor = $fujiElevatorDevices[$i]->text_value;
				$item->id = $fujiElevatorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getJetecWindDirectionData() {
			$jetecWindDirectionDevices = self::getJetecWindDirectionDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($jetecWindDirectionDevices); $i++)
			{
			// foreach($jetecWindDirectionDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->text_value = $jetecWindDirectionDevices[$i]->text_value;
				$item->id = $jetecWindDirectionDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getWeemaIaqData() {
			$weemaIaqDevices = self::getWeemaIaqDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($weemaIaqDevices); $i++)
			{
				$item = new StdClass();
				$item->temperature = $weemaIaqDevices[$i]->temp;
				$item->humidity = $weemaIaqDevices[$i]->humidity;
				$item->co = $weemaIaqDevices[$i]->co;
				$item->co2 = $weemaIaqDevices[$i]->co2_ppm;
				$item->tvoc = $weemaIaqDevices[$i]->tvoc;
				$item->hcho = $weemaIaqDevices[$i]->hcho;
				$item->pm01 = $weemaIaqDevices[$i]->pm01;
				$item->pm25 = $weemaIaqDevices[$i]->pm25;
				$item->pm10 = $weemaIaqDevices[$i]->pm10;
				$item->id = $weemaIaqDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getJetecRainMeterData() {
			$jetecRainMeterDevices = self::getJetecRainMeterDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($jetecRainMeterDevices); $i++)
			{
			// foreach($jetecRainMeterDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->text_value = $jetecRainMeterDevices[$i]->text_value;
				$item->id = $jetecRainMeterDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}

		public static function getJetecSoilMeterData() {
			$jetecSoilMeterDevices = self::getJetecSoilMeterDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($jetecSoilMeterDevices); $i++)
			{
			// foreach($jetecSoilMeterDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->text_value = $jetecSoilMeterDevices[$i]->text_value;
				$item->id = $jetecSoilMeterDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}

		public static function getSmartLampData() {
			$smartLampDevices = self::getSmartLampDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($smartLampDevices); $i++)
			{
			// foreach($smartLampDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->voltage = round($smartLampDevices[$i]->smart_lamp_voltage,2);
				$item->current = round($smartLampDevices[$i]->smart_lamp_current,3);
				$item->power = round($smartLampDevices[$i]->smart_lamp_power/1000,2);
				$item->accumulatePower = round($smartLampDevices[$i]->smart_lamp_accumulate_power/1000,2);
				$item->id = $smartLampDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getWaterMeterData($floor_id) {
			$waterMeterDevices = self::getWaterMeterDevices($floor_id);
			$itemToOutput = array();
			for($i = 0; $i < count($waterMeterDevices); $i++)
			{
			// foreach($waterMeterDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->accumulateWaterFlow = round($waterMeterDevices[$i]->accumulateWaterFlow,2);
				$item->id = $waterMeterDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getAnalogData($floor_id) {
			$devices = self::getAnalogDevices($floor_id);
			// $itemToOutput = array();
			// for($i = 0; $i < count($devices); $i++)
			// {
			// // foreach($device as $key=>$coSensorDevice) {
			// 	// $coSensorDeviceItem =
			// 	// $item = new StdClass();
			// 	$devices[$i]->decimal_value = round($devices[$i]->decimal_value,$devices[$i]->ga_decimal_digits);
			// 	// $item->id = $device[$i]->id;
			// 	// array_push($itemToOutput, $item);
			// }
			return $devices;
		}
		public static function getSolarMeterData($floor_id) {
			$solarMeterDevices = self::getSolarMeterDevices($floor_id);
			$itemToOutput = array();
			for($i = 0; $i < count($solarMeterDevices); $i++)
			{
			// foreach($solarMeterDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->elec_kwh = $solarMeterDevices[$i]->elec_kwh;
				$item->elec_kw = $solarMeterDevices[$i]->elec_kw;
				$item->id = $solarMeterDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getElectronicMeterData($floor_id) {
			$electronicMeterDevices = self::getElectronicMeterDevices($floor_id);
			$itemToOutput = array();
			for($i = 0; $i < count($electronicMeterDevices); $i++)
			{
			// foreach($electronicMeterDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->elec_kwh = $electronicMeterDevices[$i]->elec_kwh;
				$item->id = $electronicMeterDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getTemperatureSensorData() {
			$temperatureSensorDevices = self::getTemperatureSensorDevices();
			$itemToOutput = array();
			for($i = 0; $i < count($temperatureSensorDevices); $i++)
			{
			// foreach($temperatureSensorDevices as $key=>$coSensorDevice) {
				// $coSensorDeviceItem =
				$item = new StdClass();
				$item->humidity = $temperatureSensorDevices[$i]->humidity;
				$item->temp = $temperatureSensorDevices[$i]->temp;
				$item->lux = $temperatureSensorDevices[$i]->lux;
				$item->co2_ppm = $temperatureSensorDevices[$i]->co2_ppm;
				$item->co = $temperatureSensorDevices[$i]->co;
				$item->dio_type = $temperatureSensorDevices[$i]->dio_type;
				$item->decimal_value = $temperatureSensorDevices[$i]->decimal_value;
				$item->id = $temperatureSensorDevices[$i]->id;
				array_push($itemToOutput, $item);
			}
			return $itemToOutput;
		}
		public static function getDevicesStatus()
		{
			// $items = self::getAllDevices();
			return self::getAllDeviceStatuses();
	// 		$a = array();

    //   //$myfile = fopen("pingfile.txt", "w");
	// 		foreach($items as $i=>$item)
	// 		{
	// 		    $a[$item->id] = $item->status;
	// 		}

          //$a = array_merge($a,$b);

    //   return $a;

		}


		public static function getPhoneCCTVStatus()
		{
			$items = self::doGetPhoneCCTVDevices();

			$arr = array();

      //$myfile = fopen("pingfile.txt", "w");
			foreach($items as $i=>$item)
			{
				  array_push($arr, $item);
			}

          //$a = array_merge($a,$b);

      return $arr;

		}

		public static function getDevice($id)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

			 $query->where('a.id = '.$id);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getSmartLampDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 8');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getJetecWindDirectionDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 15');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getWeemaIaqDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 28');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getJetecRainMeterDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 16');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getJetecSoilMeterDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 17');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getIrtiIvaPersonDetectionDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 26');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getIrtiIvaPersonCountingDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type = 27');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getFujiElevatorDevices()
		{
			$db = JFactory::getDbo();


			$query = $db->getQuery(true);
			$query->from('`#__top_table` AS top')
					->select('DISTINCT top.id')
					->where('top.vendor = 13')
					->where('top.enable = 1');
			$db->setQuery($query);
			$elevatorTops = (array) $db->loadObjectList();
			if (count($elevatorTops) > 0) {
				$elevatorTopIds = array_column($elevatorTops, 'id');
				// return $elevatorTopIds;
				$query = $db->getQuery(true);
				$query->from('`#__device_table` AS device')
						->select('DISTINCT device.*')
						->where('device.index = 3')
						->where('device.dio_type = 3')
						->where('device.dio_id IN ('. implode(',',$db->quote($elevatorTopIds)).')');
				$db->setQuery($query);
				return (array) $db->loadObjectList();
			}
			return array();
		}
		public static function getMitsubishiElevatorDevices()
		{
			$db = JFactory::getDbo();


			$query = $db->getQuery(true);
			$query->from('`#__top_table` AS top')
					->select('DISTINCT top.id')
					->where('top.vendor = 11')
					->where('top.enable = 1');
			$db->setQuery($query);
			$elevatorTops = (array) $db->loadObjectList();
			if (count($elevatorTops) > 0) {
				$elevatorTopIds = array_column($elevatorTops, 'id');
				// return $elevatorTopIds;
				$query = $db->getQuery(true);
				$query->from('`#__device_table` AS device')
						->select('DISTINCT device.*')
						->where('device.index = 1')
						->where('device.dio_type = 3')
						->where('device.dio_id IN ('. implode(',',$db->quote($elevatorTopIds)).')');
				$db->setQuery($query);
				return (array) $db->loadObjectList();
			}
			return array();
		}
		public static function getLiuchuanElevatorDevices()
		{
			$db = JFactory::getDbo();


			$query = $db->getQuery(true);
			$query->from('`#__top_table` AS top')
					->select('DISTINCT top.id')
					->where('top.vendor = 8')
					->where('top.enable = 1');
			$db->setQuery($query);
			$elevatorTops = (array) $db->loadObjectList();
			if (count($elevatorTops) > 0) {
				$elevatorTopIds = array_column($elevatorTops, 'id');
				// return $elevatorTopIds;
				$query = $db->getQuery(true);
				$query->from('`#__device_table` AS device')
						->select('DISTINCT device.*')
						->where('device.index = 1')
						->where('device.dio_type = 3')
						->where('device.dio_id IN ('. implode(',',$db->quote($elevatorTopIds)).')');
				$db->setQuery($query);
				return (array) $db->loadObjectList();
			}
			return array();
		}
		public static function getFireAlarmFloors()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__floor_table` as floor')
						->select('floor.id, floor.name, floor.path, floor.picture')
						->where('floor.building = 207')
						->order($db->escape('floor.ordering ASC'));
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getFloorImagePath($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__floor_table` as floor')
						->select('floor.id, floor.path')
						->where('floor.id =' .$id);
			$db->setQuery($query);
			$result =  (array) $db->loadObjectList();
			// return $result;
			if (count($result) > 0)
			{
				 return $result[0]->path;
			} else
			{
				return "";
			}
		}
		public static function getDeviceImagePath($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` as device')
						->select('device.id, device.status, device.yellow, device.red, device.green')
						->where('device.id =' .$id);
			$db->setQuery($query);
			$result =  (array) $db->loadObjectList();
			// return $result;
			if (count($result) > 0)
			{
				 return $result[0]->red;
			} else
			{
				return "";
			}
		}
		public static function getFireAlarmDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__floor_table` as floor')
						->select($db->quoteName(array('device.id','device.floor', 'device.info', 'device.dio_id' , 'device.type', 'device.fontsize', 'device.status', 'device.pointx', 'device.pointy', 'device.floor', 'device.note','device.width','device.height','device.color')))
						->join('LEFT' , $db->quoteName('#__device_table', 'device'). ' ON ' . $db->quoteName('device.floor'). ' = ' . $db->quoteName('floor.id') )
						->order($db->escape('floor.ordering ASC'))
						->where('((device.status !=1 and device.dio_type = 1) or (dio_id = 0 and `type`=3) ) and floor.building = 207 and device.enable = 1');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getFireAlarmDevicesCount()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__floor_table` as floor')
						->select($db->quoteName(array('device.id','device.floor', 'device.info', 'device.dio_id' , 'device.type', 'device.fontsize', 'device.status', 'device.pointx', 'device.pointy', 'device.floor', 'device.note','device.width','device.height','device.color')))
						->join('LEFT' , $db->quoteName('#__device_table', 'device'). ' ON ' . $db->quoteName('device.floor'). ' = ' . $db->quoteName('floor.id') )
						->order($db->escape('floor.ordering ASC'))
						->where('((device.status !=1 and device.dio_type = 1)) and floor.building = 207 and device.enable = 1');
			$db->setQuery($query);
			return count((array) $db->loadObjectList());
		}
		public static function getPmSensorDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type IN (41)');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getCoSensorDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type IN (7,34,40)');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}

		public static function getSolarMeterDioTypes()
		{
			return array(19,33,37);
		}
		public static function getWaterMeterDioTypes()
		{
			return array(6,20,32);
		}
		public static function getElectronicMeterDioTypes()
		{
				// 4=>'温濕度計',
			// 5=>'電表',
			// 6=>'水表',
			// 7=>'一氧化碳',
			// 8=>'智慧路燈',
			// 9=>'巧力電表',
			// 10=>'大同電表',
			// 11=>'永嘉溫濕度計',
			// 12=>'Bender PEM333 電表',
			// 13=>'Bender PEM575 電表',
			// 14=>'士林電機電表shihlin',
			// 15=>'久德風向感測器',
			// 16=>'久德雨量計',
			// 17=>'久德土壤計',
			// 18=>'JNC智慧空氣偵測儀',
			// 19=>'新望太陽能
			//cic
			return array(5,9,10,12,13,14,21,23,24,25,29,31,36,43,44,47,50,51);
		}
		public static function getWaterMeterDevices($floor_id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('(device.dio_type = 6 or device.dio_type = 20 or device.dio_type = 32) and device.floor =' . $db->quote($floor_id));

			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getAnalogDevices($floor_id)
		{
			$db = JFactory::getDbo();

			$query = $db->getQuery(true);
			$query->from('`#__device_table`')
			->select("GROUP_CONCAT(replace(info,'C$','')) as tids")
			->where('dio_type =999')
			->where('enable = 1')
			->where("info like '%C$%'");
			$db->setQuery($query);
			$tids = $db->loadObject()->tids;

			// $db->setQuery($query);
			// $fake_dios = (array) $db->loadColumn();


			$query = $db->getQuery(true);
			$query->from('`#__device_table`')
					->select('id,ga_is_lookup_table,ga_map_table,dio_type, CAST(ROUND(decimal_value,ga_decimal_digits) as double) as decimal_value, ga_decimal_digits, ga_display_prefix, ga_unit, text_value')
					->where('dio_type in (22,35)');

			if (!empty($tids))
			{
				$query->where('id in ('.$tids .') or `floor` ='. $db->quote($floor_id));
			}
			else
			{
				$query->where('`floor` ='. $db->quote($floor_id));
			}

			$db->setQuery($query);
			$result =  (array) $db->loadObjectList();
			foreach($result as $i=>$item)
			{

				try {

					// $result[$i]['new_value'] = 1;
					if ($item->ga_is_lookup_table)
					{
						if (!empty($item->ga_map_table))
						{
							// $result[$i]->ga_map_table = '{"-5": "B5", "-4": "B4", "-3": "B3", "-2": "B2", "-1": "B1", "0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16", "17": "17", "18": "18", "19": "19", "20": "20", "21": "21", "22": "22", "23": "23", "24": "24", "25": "25", "26": "26", "27": "27", "28": "28", "29": "29", "30": "30", "31": "31", "32": "32", "33": "33", "34": "34", "35": "35", "36": "36", "37": "37", "38": "38", "39": "39", "40": "40", "41": "41", "42": "42", "43": "43", "44": "44", "45": "45", "46": "46", "47": "47", "48": "48", "49": "49", "50": "50", "51": "51", "52": "52", "53": "53"}';
							$table = json_decode($result[$i]->ga_map_table,true);
							$index = intval($item->decimal_value);
							$result[$i]->text_value = $table[$index];
							// $result[$i]->table = $item->ga_map_table;
							if (json_last_error() == JSON_ERROR_NONE)
							{

								$result[$i]->error = true;
							}
							// else
							// {
							// 	$result[$i]->error = $table;
							// }
						}
					}
					else
					{
						$result[$i]->error = false;
					}
				} catch (\Throwable $th) {
					throw $th;
				}
				unset($result[$i]->ga_map_table);
			}
			return $result;
		}
		public static function getElectronicMeterDevices($floor_id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.floor = '. $db->quote($floor_id))
					->where('device.dio_type IN (5,9,10,12,13,14,21,23,24,25,29,31,36,43,44,50)');

			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getSolarMeterDevices($floor_id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					// ->where('device.floor = '. $db->quote($floor_id))
					->where('device.dio_type IN (19,33,37)');

			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getTemperatureSensorDevices()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->from('`#__device_table` AS device')
					->select('DISTINCT device.*')
					->where('device.dio_type in (4,11,18,30,38,39,42,46,48,49)');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
		}
		public static function getAllDeviceStatuses()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true)
						->select('dev.id,dev.status')
						->from('`#__device_table` as dev')
						->where('dev.state=1')
						->where('dev.enable=1')
						->order('dev.id DESC');
			$db->setQuery($query);
			return $db->loadAssocList('id','status');

		}
		public static function getAllDevices()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       $query->where('a.enable = 1');

			 $query->order($db->escape('a.id DESC'));

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function doGetPhoneCCTVDevices()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       $query->where('a.enable = 1');

			 $query->where('a.open = 1');

       $query->where('a.type = 1');

			 $query->order($db->escape('a.id DESC'));

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getOnlineIPDevices()
    {
         $items = self::getOnlineExpireGroupItem();

         $myarr = array();
         foreach($items as $i=>$item)
				 {
					 self::resetOnlineGroup($item);
					 $myitems = self::getIPDevices($item->id,1);
					 foreach($myitems as $i=>$myitem)
					 {
               array_push($myarr, $myitem);
           }
					 //$obj[] = self::getIPDevices($item->id,1);
				 }

         return $myarr;


    }

	public static function getOpenDevices()
    {
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.enable = 1');

		 $query->where('a.open = 1');
		 $query->order($db->escape('a.update DESC ,a.update_time  DESC'));

		 $query->setLimit(1);
		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();
		 if(count($items)==0)
		 {
			// Create a new query object.
			$query = $db->getQuery(true);

			// Select the required fields from the table.
			$query
					->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');

			$query->where('a.enable = 1');

			$query->where('a.open = 0');
			$query->order($db->escape('a.update DESC ,a.update_time  DESC'));

			$query->setLimit(1);
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			foreach($items as $i=>$item)
			{
				$id = +$item->dio_alarm;
			}
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query	->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');
			$query->where($db->quoteName('id') . ' = ' . $db->quote($id));
			$query->setLimit(1);
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			foreach($items as $i=>$item)
			{
			$result_item = +$item->cctv_timeout;
			}
			if($result_item=="")
			{
				$result_item = 0;
			}
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
			$query
					->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');

			$query->where('a.enable = 1');

			$query->where('a.is_change_setting = 0');

			$query->where('TIMESTAMP(a.last_alarm_time)>=date_sub(now(),  INTERVAL ' . $db->quote($result_item) . ' second) ');
			$query->where('a.open = 0');
			$query->order($db->escape('a.last_alarm_time DESC'));

			$query->setLimit(1);
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
		 }
		 $device_brands = FloorHelpersFloor::getDeviceBrands();
		 for ($i=0; $i < count($items); $i++) {
			 $items[$i]->web_view_subpath = "/";
			 $items[$i]->web_view_port = "80";
			 $items[$i]->web_view_scheme = "http";
			 if ($items[$i]->device_brand_id !=0) {
				for ($j=0; $j < count($device_brands); $j++) {
					// $device_brand = $device_brands[$j];
					if ($device_brands[$j]->id == $items[$i]->device_brand_id) {
						$items[$i]->web_view_subpath = $device_brands[$j]->web_view_subpath;
						$items[$i]->web_view_port = $device_brands[$j]->web_view_port;
						$items[$i]->web_view_scheme = $device_brands[$j]->web_view_scheme;
					}

				}
			 }
		 }
		 return $items;
		}
		public static function getOpenDevicesphone()
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
			$query
			->select('DISTINCT a.*');

		$query->from('`#__device_table` AS a');

		$query->where('a.state = 1');

		$query->where('a.enable = 1');

		 //$query->where('a.type = 3');

		 //$query->where('a.dio_id = 0');

		 $query->where('a.open = 1');

		$query->where('a.path != ""');
		$query->where('a.type = 1');
		 $query->order($db->escape('a.update DESC ,update_time  DESC'));

		$query->setLimit(1);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();
		 $device_brands = FloorHelpersFloor::getDeviceBrands();
		 for ($i=0; $i < count($items); $i++) {
			 $items[$i]->web_view_subpath = "/";
			 $items[$i]->web_view_port = "80";
			 $items[$i]->web_view_scheme = "http";
			 if ($items[$i]->device_brand_id !=0) {
				for ($j=0; $j < count($device_brands); $j++) {
					// $device_brand = $device_brands[$j];
					if ($device_brands[$j]->id == $items[$i]->device_brand_id) {
						$items[$i]->web_view_subpath = $device_brands[$j]->web_view_subpath;
						$items[$i]->web_view_port = $device_brands[$j]->web_view_port;
						$items[$i]->web_view_scheme = $device_brands[$j]->web_view_scheme;
					}

				}
			 }
		 }
		 return $items;
		}

		public static function getOfflineDevices()
    {
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
				 ->select('DISTINCT a.*');

		 $query->from('`#__device_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.enable = 1');

		 $query->where('a.status = 3');

     $query->order($db->escape('a.update DESC'));

     $query->order($db->escape('a.update_time DESC'));

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
		}

		public static function getOfflineIPDevices()
    {
		    $items = self::getOfflineExpireGroupItem();

			  $myarr = array();
			  foreach($items as $i=>$item)
			  {
				    self::resetOfflineGroup($item);
				    $myitems = self::getIPDevices($item->id,3);
				    foreach($myitems as $i=>$myitem)
				    {
						    array_push($myarr, $myitem);
				    }

		  	}

			  return $myarr;
		}

		public static function getIPDevices1($is_status)
    {
        if($is_status == 1)
				{
					return self::getOnlineIPDevices();
				}
				else
				{
					return self::getOfflineIPDevices();
				}

    }

		public static function getIPDevicesByIpaddr($ipaddr)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       //$query->where('a.type = 2 OR a.type = 3');

       $query->where('a.dio_id= 0');

			 $query->where($db->quoteName('a.info') . ' = ' . $db->quote($ipaddr));

       //$query->where('a.info= "'. $ipaddr.'"');

			 //$query->where('a.enable = 1');

		   $db->setQuery($query);
       //echo($query);


       //echo('<br>');
			 //echo($query);
		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getIPDevices($group,$is_status,$is_filter=false)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
       $filter = 'a.*';
			 if($is_filter != false)
			 {
				  $filter = 'a.id,a.info,a.note,a.status';
			 }
			 $query
		   ->select('DISTINCT '.$filter);

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       //$query->where('a.type = 2 OR a.type = 3');

       $query->where('a.group = '. $group);

       if($is_status > 0)
			 {
           $query->where('a.status = '.$is_status);
       }

			 $query->where('a.enable = 1');

		   $db->setQuery($query);

       //echo('<br>');
			 //echo($query);
		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getPhoneDevicesByGroup($group)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.

			 $filter = 'a.id,a.info,a.note,a.status';

			 $query
		   ->select('DISTINCT '.$filter);

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       //$query->where('a.type = 2 OR a.type = 3');

       $query->where('a.group = '. $group);

			 $query->where('a.enable = 1');

		   $db->setQuery($query);

       //echo('<br>');
			 //echo($query);
		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function getSIPDevices($caller=null)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

       $query->where('a.type = 1');

       $query->where('a.enable = 1');

       if($caller != null)
			 {
          $query->where('a.info = '.$caller);
			 }
		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function getPhoneDevices($path)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__device_table` AS a');

       $query->where('a.state = 1');

			 $query->where('a.dio_id = 0');

       $query->where('a.info = '.$path);

       $query->where('a.enable = 1');

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}


		public static function getTopPhoneCCTVDevices($obj)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

       $query->where('a.state = 1');

       $query->where('a.note1 = '.self::$tvpage);

			 $query->where('a.caller = '.$obj->caller);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function closeCCTVDevice($ids)
    {
				foreach($ids as $i=>$item)
				{
					  self::reset_cctv($item['id']);

				}

		}

		public static function getLogStatus($ids)
    {
        $arr = array();

				foreach($ids as $i=>$item)
				{
				    $items = self::getDevicelog($item['id']);

						if(count($items))
						    array_push($arr,$items[0]);

				}

				return $arr;
		}

    public static function getDevicelog($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__devicelog_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.id = '.$id);

     $query->order($db->escape('id DESC'));
		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

		}
		public static function getSIPInfo()
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

       $query->where('a.state = 1');

       $query->where('a.note1 = '.self::$page);

       $query->where('a.type = 1');

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function updateDevicelog($item,$obj)
		{
			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__devicelog_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('status') . ' = ' . $db->quote($obj->status))
	    ->set($db->quoteName('check_name') . ' = ' . $db->quote($obj->check_name))

	    ->set($db->quoteName('check_date') . ' = ' . $db->quote($obj->check_date))
	    ->set($db->quoteName('check_time') . ' = ' . $db->quote($obj->check_time))

	    ->where($db->quoteName('id') . ' = ' . $db->quote($item->id));

	    $db->setQuery($query)->execute();
      //echo($query);

		}
	public static function find_alarm_device($obj)
    {
		// Create a new query object.
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
		   ->select('DISTINCT a.*');

		$query->from('`#__device_table` AS a');

		$query->where('a.state = 1');
	    $query->where('a.info = '.$db->quote($obj->alarm));

		$db->setQuery($query);
       //echo($query);
		$items = (array) $db->loadObjectList();

		return $items;

    }
	private static function update_open($obj)
	{
		$items1 = self::find_alarm_device($obj);

		$open = 0;
		if($obj->status == 3)
		{
			$open = 1;

			if(count($items1) > 0 && $items1[0]->enable == 1)
			{

				//echo('action '.$obj->dio_alarm);
				$items2 = self::get_dio_top($items1[0]->dio_alarm);

				if(count($items2))
				{

					$db = JFactory::getDbo();
					$fields1 = array();
					//return;
			  		foreach(range(0,3) as $i)
					{
						$fields1 = TopHelpersUtility::set_open_path($items2[0],$db,$open,$i,$fields1);
			  		}
					if(count($fields1) == 0)    return;

					$conditions = array(
					$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				  );
					$query = $db->getQuery(true);
					$query->update($db->quoteName('#__device_table'))->set($fields1)->where($conditions);

			  		$db->setQuery($query)->execute();
					  //echo($query);
					  return;

				}
			}
		}
	}
	public static function update_device_status($device_id, $status)
	{
		$db = JFactory::getDbo();
		$query = $db->getQuery(true);

		// Select the required fields from the table.
		$query
		   ->select('DISTINCT a.*');

		$query->from('`#__device_table` AS a');

		$query->where('a.state = 1');
	    $query->where('a.id = '.$db->quote($device_id));

		$db->setQuery($query);
       //echo($query);
		$items = (array) $db->loadObjectList();

        if(count($items))
		{
			$obj->id = $items[0]->id;
			// $update = date ("Y/m/d");
			// $update_time = date ("H:i:s");



			$query = $db->getQuery(true)
			->update($db->quoteName('#__device_table'))
			//->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
			->set($db->quoteName('status') . ' = ' . $db->quote($status))

			// ->set($db->quoteName('update') . ' = ' . $db->quote($update))
			// ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
			->where($db->quoteName('id') . ' = ' . $db->quote($device_id));
			$db->setQuery($query)->execute();
			$mylog = self::findDevicelog($items[0],1);

			if(count($mylog) && ($mylog[0]->status == 1))
			{
				//echo('update_alarm_device_state 3');
				if($status == 1)
				{
					$items[0]->status = 1;
					self::update_devicelog($mylog[0],$items[0]);
				}
			}
			else
			{
				if($status == 3)
				{
					$items[0]->status = 3;
					$items[0]->type = '失聯';
					$items[0]->need_alarm = 1;
					self::add_to_devicelog($items[0]);
				}

			}


		}

	}
	public static function update_alarm_device_state($obj)
	{
        $items = self::find_alarm_device($obj);

		//secho('update_alarm_device_state 1');
        if(count($items))
		{
			//echo('update_alarm_device_state 2');
			$obj->id = $items[0]->id;
			self::update_open($obj);
			self::update_device_state($obj);

			$mylog = self::findDevicelog($items[0],1);

			if(count($mylog) && ($mylog[0]->status == 1))
			{
				//echo('update_alarm_device_state 3');
				if($obj->status == 1)
				{
					$items[0]->status = 1;
					self::update_devicelog($mylog[0],$items[0]);
				}
			}
			else
			{
				if($obj->status == 3)
				{
					//echo('update_alarm_device_state 4');
					$items[0]->check_name = '';
					$items[0]->type = '保全';
					$items[0]->end_status = 1;
					$items[0]->status = 105;
					$items[0]->check_date = date("Y/m/d");
					$items[0]->check_time = date("H:i:s");
					self::do_add_to_devicelog($items[0]);
				}

			}


		}

	}

		public static function update_device_state($obj)
		{
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__device_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('status') . ' = ' . $db->quote($obj->status))

	    ->set($db->quoteName('update') . ' = ' . $db->quote($update))
	    ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
      ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

	    if(isset($obj->open))
	    {
		      $query->set($db->quoteName('open') . ' = ' . $db->quote($obj->open));
		      $query->set($db->quoteName('path') . ' = ' . $db->quote($obj->path));
					$query->set($db->quoteName('caller') . ' = ' . $db->quote($obj->caller));
					$query->set($db->quoteName('callee') . ' = ' . $db->quote($obj->callee));
					$query->set($db->quoteName('open1') . ' = ' . $db->quote($obj->open1));
		      $query->set($db->quoteName('path1') . ' = ' . $db->quote($obj->path1));
					$query->set($db->quoteName('open2') . ' = ' . $db->quote($obj->open2));
		      $query->set($db->quoteName('path2') . ' = ' . $db->quote($obj->path2));
					$query->set($db->quoteName('open3') . ' = ' . $db->quote($obj->open3));
			  $query->set($db->quoteName('path3') . ' = ' . $db->quote($obj->path3));
			  $query->set($db->quoteName('device_brand_id') . ' = ' . $db->quote($obj->device_brand_id));
			// 	$query->set($db->quoteName('device_brand_id1') . ' = ' . $db->quote($obj->device_brand_id1));
			// 	$query->set($db->quoteName('device_brand_id2') . ' = ' . $db->quote($obj->device_brand_id2));
			// 	$query->set($db->quoteName('device_brand_id3') . ' = ' . $db->quote($obj->device_brand_id3));
	   }


	   $db->setQuery($query)->execute();
  //echo($query);
		}

		public static function update_group_cnt($obj)
		{
			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__top_table'))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('online_cnt') . ' = ' . $db->quote($obj->online_cnt))
			->set($db->quoteName('offline_cnt') . ' = ' . $db->quote($obj->offline_cnt))

	    ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

	    $db->setQuery($query)->execute();

      //echo($query);
		}


		public static function conv_string_state($mystatus)
		{

			if($mystatus == "Registered")
			    return 1;
			else if($mystatus == "ONCALL")
          return 2;
			else
			    return 3;

    }


    public static function one_second_pass()
    {
	      $items = self::getIPGroupItem(0);

				foreach($items as $i=>$item)
				{
					$is_change = false;
					if($item->online_cnt > 0)
					{
						$is_change = true;
						$item->online_cnt = $item->online_cnt-1;
					}
					if($item->offline_cnt > 0)
					{
						$is_change = true;
						$item->offline_cnt = $item->offline_cnt-1;
					}

					if($is_change == true)
					{
              self::update_group_cnt($item);
					}

				}
    }

		public static function sendMessage1($items)
	  {
			$numbers = self::getCallee();

      if(count($numbers) == 0)    return;

      if(count($items) == 0)    return;

			$callee = '';
			foreach($numbers as $i => $number)
			{
      		$callee = $number->number.'-'.$callee;
			}

			$mymsg = array();

			foreach($items as $i => $item)
			{
      		array_push($mymsg,$item);
			}

			if(count($mymsg) > 0)
			{
				self::do_sendMessage1($mymsg,$callee);

			}

	  }

	  public static function do_sendAlarmMessage1($obj)
	  {
			$desc = $obj->msg_context;

            $callee = '';

			$addr = preg_split("/[\s,]+/", $obj->msg_number);

			foreach($addr as $i=>$item)
			{
			    $callee = $item.'-'.$callee;

			}

			$in = 'desc='.$desc.'&callee='.$callee;//input, transfer msg to server side
            self::do_write_to_socket($in);

	  }

		public static function do_sendMessage1($items,$callee)
	  {
      if(count($items) == 0)    return;

			$desc = '';
			foreach($items as $i => $item)
			{
      		$desc = $item->note;
					break;
			}

			$in = 'desc='.$desc.'&callee='.$callee;//input, transfer msg to server side

			self::do_write_to_socket($in);

	  }
		public static function sendMessage($items)
	  {
			$numbers = self::getCallee();

      if(count($numbers) == 0)    return;

      if(count($items) == 0)    return;

			$callee = '';
			foreach($numbers as $i => $number)
			{
      		$callee = $number->number.'-'.$callee;
			}

			$mymsg = array();

			foreach($items as $i => $item)
			{
      		array_push($mymsg,$item);

					if(count($mymsg) >= 30)
					{
						self::do_sendMessage($mymsg,$callee);
						$mymsg = array();
					}
			}

			if(count($mymsg) > 0)
			{
				self::do_sendMessage($mymsg,$callee);

			}

	  }

	  public static function do_sendMessage($items,$callee)
	  {
          if(count($items) == 0)    return;

		  $desc = '';
		  foreach($items as $i => $item)
		  {
      		$desc = $item->note.','.$desc;
		  }

          if($items[0]->status == 1)
              $desc = $desc . ' 正常 ';
		  else
			  $desc = $desc . ' 警報 ';

          $desc = $desc . date("H:i:s");

	      $in = 'desc='.$desc.'&callee='.$callee;//input, transfer msg to server side

	      self::do_write_to_socket($in);

	  }


	  public static function setCallInfo($obj)
	  {

          $myaccounts = TopHelpersUtility::getMyAccount();

          if(count($myaccounts) == 0)    return;

		  $desc = 'callee='.$obj->callee.'&msg=強制撥出 '.date("H:i:s").'&domain='.$myaccounts[0]->domain;

		  $in = 'call='.$obj->caller.'&'.$desc;//input, transfer msg to server side

		  echo($in);
		  self::do_write_to_socket($in);


          $items = self::getSIPDevices($obj->caller);

		  if(count($items) == 0)    return;

          $items[0]->check_name = $obj->check_name;
          self::phoneCalloLog($items[0]);

	  }

	  public static function do_sendAlarmMessage($items,$callee)
	  {
          if(count($items) == 0)    return;

          $myaccounts = TopHelpersUtility::getMyAccount();

          if(count($myaccounts) == 0)    return;

		  $desc = '';

		  foreach($items as $i => $item)
		  {
      		$desc = 'callee='.$item->callee.'&msg=警報 '.date("H:i:s").'&domain='.$myaccounts[0]->domain.$desc;
		  }

		  $in = 'call='.$callee.'&'.$desc;//input, transfer msg to server side

		  self::do_write_to_socket($in);

	  }
	  public static function getMaxLogId()
	  {
		$db = JFactory::getDbo();
		$query = $db->getQuery(true)
				->select('*')
				->from('`#__devicelog_table` as devicelog')
				->where('state = 1')
				->order('id DESC')
				->setLimit(1);
		return (array)$db->loadObjectList();
	  }
		public static function findMaxLogId()
		{
			$d = new DateTime();
			$d->modify('first day of 3 month ago');
			$dateLowBoundLimitString = $d->format('Y/m/d');

			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $supressed_node_ids = self::getDioSupressedAlarmNodes();
			  $query = $db->getQuery(true);
			  $query->select('id')
			  		->from('`#__devicelog_table`')
					->setLimit(1)
					->order($db->escape('id DESC'));
			  $db->setQuery($query);
			  $maxId = (array) $db->loadObjectList();
			  if (count($maxId))
			  {
				$maxId = $maxId[0]->id-300000;
			  }
			  else
			  {
				$maxId = 1;
			  }
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__devicelog_table` AS a');
		//    $where_string = "(a.trigger_alarm = 1 AND a.state = 1"
		   $query->where('a.trigger_alarm = 1');
		   $query->where('a.state = 1');
		   if (count($supressed_node_ids) >0)
		   {
			//    $where_string =
			   $query->where('a.device_id not in ('. implode(',', $supressed_node_ids) . ')');
		   }
			 $query->where('a.note5 = 1');
			 $query->where("a.status = 1");
			 $query->where('a.create_datetime >='.$db->quote($dateLowBoundLimitString));
			 $query->where('a.id >='. $db->quote($maxId));

			 $query->where("a.check_name != '警報'");
			//  $query->orWhere("(a.check_name = 'sip server' and a.status = '2')");

       $query->order($db->escape('id DESC'));
			$query->setLimit(1);

			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			if (count($items) > 0)
			{
				$query1 = $db->getQuery(true)
							->from('`#__device_table` AS device')
							->select('device.*')
						 	->where('device.id = ' . $db->quote($items[0]->device_id))
							->setLimit(1);
				$db->setQuery($query1);
				$devices = (array) $db->loadObjectList();
				if (count($devices) > 0)
				{
					if (empty($devices[0]->alarm_sound_file))
					{
						$items[0]->alarm_sound_file = 'basic_alarm.mp3';
					}
					else
					{
						$items[0]->alarm_sound_file = $devices[0]->alarm_sound_file;
					}
				}
				else
				{
					$items[0]->alarm_sound_file = 'basic_alarm.mp3';
				}
			}
		   return $items;

		}


		public static function findDevicelog($obj,$last=0)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		   ->select('DISTINCT a.*');

		   $query->from('`#__devicelog_table` AS a');

		   $query->where('a.state = 1');

		   $query->where('a.device_id = '.$obj->id);

       if($last == 1)
			 {
				 $query->setLimit(1);
			 }
			 else
		       $query->where('a.status = "1" OR a.status = "2"');

       $query->order($db->escape('id DESC'));

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}
		public static function update_devicelog($item,$obj,$is_do=0)
		{
      if($obj->condition == 0)
			{
		    	$status = 3;
			    if($is_do == 1 || $obj->status == 1)
			    {
               if($item->status == 1)
					     {
						       $status = 3;
					     }
					     else if($item->status == 2)
					     {
						      $status = 0;
					     }
			   }
			   else if($obj->status == 3)
			   {
	           return;
			   }
      }
			else
			{
				$status = $obj->status;
			}
      echo("</br>".$obj->status."</br>");
      $check_status = self::$status[$obj->status];

			$end_date = date("Y/m/d");
			$end_time = date("H:i:s");

			$db = JFactory::getDbo();

	    $query = $db->getQuery(true)
	    ->update($db->quoteName('#__devicelog_table'))

      ->set($db->quoteName('check_status') . ' = ' . $db->quote($check_status))

	    //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
	    ->set($db->quoteName('status') . ' = ' . $db->quote($status))

			->set($db->quoteName('end_date') . ' = ' . $db->quote($end_date))
			->set($db->quoteName('end_time') . ' = ' . $db->quote($end_time))

	    ->where($db->quoteName('id') . ' = ' . $db->quote($item->id));

      //echo($query);
	    $db->setQuery($query)->execute();

		}
	private static function do_add_to_devicelog($obj,$status=1)
	{
		$db = JFactory::getDbo();
	    $db->setQuery('SELECT MAX(ordering) FROM #__devicelog_table');
	    $max = $db->loadResult();
	    $ordering = $max+1;

	    $created_by = JFactory::getUser()->name;
	    $checked_out = false;
	    $state = 1;

		$check_status = self::$status[$obj->status];

		$items = self::get_my_floor($obj->floor);
		$str_building = '';
		$str_floor = '';
		$building_id = 0;
		if(count($items) > 0)
		{
			$str_floor = $items[0]->name;
			$items1 = self::getBuildingItem($items[0]->building);
			if(count($items1) > 0)
			{
            	$str_building = $items1[0]->name;
				$building_id = $items1[0]->id;
			}
		}

      	$info = $obj->info;
		if($obj->dio_id > 0)
		{
			$info = $info ." 第 ".$obj->index.' 號';
		}
		if($obj->timeout > 0)
		{
			$info = $info." 時間".$obj->timeout.' 秒';
		}

      	$di_alarm = 0;
		if($obj->dio_type == 1)
		{
       		$di_alarm = 1;
			$mysuperitems = self::getThisGroupItem($obj->supergroup);
			$myitems = self::getThisGroupItem($obj->group);

        	$obj->type = "警報";
			if(count($mysuperitems))
				$obj->type = $mysuperitems[0]->name.' ';

			if(count($mysuperitems))
				$obj->type = $obj->type . $myitems[0]->name;

		}

		if(isset($obj->need_alarm))
		{
			$di_alarm = 1;
		}
		$desc = $info;
      	$obj->desc = $desc;
		// Create a new query object.
      	$query = $db->getQuery(true);
      	$date = date("Y/m/d");
		$time = date("H:i:s");
      	// Insert columns.

		$vendors = TopHelpersUtility::findDeviceVendor($obj->supergroup);

		$note = "";
		if(count($vendors))
		  $note = $vendors[0]->name." ".$vendors[0]->phone;
		$columns = array(
            'ordering',
            'state',
            'checked_out',
            'created_by',
            'type',
			'building',
			'floor',
			'name',
			'name_note',
			'date',
			'time',
			'device_id',
			'status',
			'desc',
			'check_status',
			'end_status',
			'note2',
			'note3',
			'note4',
			'note5',
			'note',
			'trigger_alarm',
			'create_datetime'
        );

		// Insert values.
		$values = array(
			$db->quote($ordering),
			$db->quote($state),
			$db->quote($checked_out),
			$db->quote($created_by),
			$db->quote($obj->type),
			$db->quote($str_building),
			$db->quote($str_floor),
			$db->quote($obj->info),
			$db->quote($obj->note),
			$db->quote($date),
			$db->quote($time),
			$db->quote($obj->id),
			$db->quote($status),
			$db->quote($desc),
			$db->quote($check_status),
			$db->quote($obj->end_status),
			$db->quote($obj->floor),
			$db->quote($building_id),
			$db->quote($obj->group),
			$db->quote($di_alarm),
			$db->quote($note),
			$db->quote($obj->trigger_alarm),
			$db->quote(str_replace('/','-',$date). ' '. $time)
		);


    	//  JLog::add($obj->check_name, JLog::INFO, 'jerror');
      	//if(false)
		if(!empty($obj->check_name))
		{

			$columns1 = array(
				'check_name',
				'check_date',
			     'check_time'

			);

          	$columns = array_merge($columns,$columns1);

			// Insert values.
          	$values1 = array(
            	$db->quote($obj->check_name),
            	$db->quote($obj->check_date),
            	$db->quote($obj->check_time),

            );

          $values = array_merge($values,$values1);
		}

      	// Prepare the insert query.
      	$query
            ->insert($db->quoteName('#__devicelog_table'))
            ->columns($db->quoteName($columns))
            ->values(implode(',', $values));

        // Set the query using our newly populated query object and execute it.
        $db->setQuery($query);
		//echo($query);
        $db->execute();

	}

		public static function add_to_devicelog($obj)
		{
			if($obj->state == 2)    return;

      $mylog = self::findDevicelog($obj,1);

			if(count($mylog) && ($mylog[0]->status == 1 || $mylog[0]->status==2))
			{
				  $obj->condition = 0;
          self::update_devicelog($mylog[0],$obj);

			}else
			{

          self::do_add_to_devicelog($obj);
      }

    }
		public static function save_device_status2($obj)
		{
			  $items = self::getSIPDevices();
			  $now_status = 3;
			  if($obj->status == "online")
            $now_status = 1;

				foreach($obj->numbers as $i => $item)
				{
					foreach($items as $i1 => $item1)
					{
							//$item1->status = 2;
							if($item1->info == $item)
							{
									if($item1->status != $now_status)
									{
										$item1->status = $now_status;
										$item1->type = '斷線';
										$item1->need_alarm = 1;
										self::update_device_state($item1);
										self::add_to_devicelog($item1);

									}

									break;
							}
					}

				}

		}
		public static function save_device_status($json,$mypoint)
		{
		     $mystatus = $json["extension_status"];

        //echo($mystatus);
        $online_item = array();
        $offline_item = array();

		    $obj = new stdClass;
				foreach($mystatus as $i => $item)
				{
					$status1 = self::conv_string_state($item["status"]);

					if($status1 == 2)    continue;
		      foreach($mypoint as $i1 => $item1)
					{
						  //$item1->status = 2;
						  if($item1->info == $item["extension_number"])
							{

								  //$item1->status = $arr[$item["status"]];

									if($item1->status != $status1)
									{
										$status2 = $item1->status;
										$item1->status = $status1;
                    $item1->type = '斷線';
										$item1->need_alarm = 1;
										self::update_device_state($item1);
										self::add_to_devicelog($item1);
										if($item1->status == 1)
										{
											array_push($online_item, $item1);
										}
										else if($item1->status == 3)
										{
											array_push($offline_item, $item1);
										}
									}

									break;
							}
				  }

				}

				self::sendMessage($offline_item);
				self::sendMessage($online_item);


		    //echo(json_encode($arr));
				//echo(json_encode($mypoint));
		}
		//deprecated
		// public static function get_devicelogAlarm()
		// {
		// 	// Create a new query object.
		// 	$db = JFactory::getDbo();
		// 	$query = $db->getQuery(true);

		// 	// Select the required fields from the table.
		//  $query
		//  ->select('DISTINCT a.*');

		//  $query->from('`#__devicelog_table` AS a');

		//  $query->where('a.state = 1');
		//  $query->where('a.note5 = 1');

		//  $orderCol  = 'id';
		//  $orderDirn = 'DESC';

		//  if ($orderCol && $orderDirn)
		//  {
		// 		 $query->order($db->escape($orderCol . ' ' . $orderDirn));
		//  }

		//  $db->setQuery($query);

		//  $items = (array) $db->loadObjectList();

		//  return $items;

		// }
		public static function get_max_note5_equals_to_1_device_log_id() {
			$db = JFactory::getDbo();
			$query = $db->getQuery(true)
						->select('devicelog.*')
						->from('`#__devicelog_table` AS devicelog')
						->where('devicelog.state = 1')
						->where('devicelog.note5 = 1')
						->setLimit(1)
						->order($db->escape('id desc'));
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			return $items;
		}
		public static function get_latest_device_logs($limit)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true)
						->select('devicelog.*')
						->from('`#__devicelog_table` AS devicelog')
						->where('devicelog.state = 1')
						->setLimit($limit)
						->order($db->escape('id desc'));
			$db->setQuery($query);
			$items = (array) $db->loadObjectList();
			return $items;
		}
		public static function get_devicelog1()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__devicelog_table` AS a');

		 $query->where('a.state = 1');
		 $query->where('a.note1 = 0');

     $query->setLimit(30);

		 $orderCol  = 'id';
		 $orderDirn = 'DESC';

		 if ($orderCol && $orderDirn)
		 {
				 $query->order($db->escape($orderCol . ' ' . $orderDirn));
		 }

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

		}
		//deprecated
		// public static function get_devicelog()
		// {
        // $items = self::get_devicelogAlarm();
		// 		$items1 = self::get_devicelog1();

		// 		//JLog::add(count($items), JLog::WARNING, 'jerror');

		// 		$items = array_merge($items,$items1);

		//     return $items;

		// }

		public static function getCallee()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__ipalarm_table` AS a');

		 $query->where('a.state = 1');

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;

		}

		public static function delete_item($obj)
		{
		    $db = JFactory::getDbo();

	            // Create a new query object.
	            $query = $db->getQuery(true);

							$conditions = array(
			        $db->quoteName('id') . ' = ' . $obj,
							);

				      $query->delete($db->quoteName('#__floor_table'));
				      $query->where($conditions);

				      $db->setQuery($query);

	             $db->execute();
	             //JLog::add($query, JLog::WARNING, 'jerror');


		}


		public static function delete_floor_device($obj)
		{
		    $db = JFactory::getDbo();

	            // Create a new query object.
	            $query = $db->getQuery(true);

	            // delete all custom keys for user 1001.
	            $conditions = array(
								$db->quoteName('floor') . ' = ' . $obj);


	             $query->delete($db->quoteName('#__device_table'));
	             $query->where($conditions);

	             $db->setQuery($query);
	             $db->execute();

		}

		public static function delete_device($obj)
		{
		    $db = JFactory::getDbo();

	            // Create a new query object.
	            $query = $db->getQuery(true);

	            // delete all custom keys for user 1001.
	            $conditions = array(
								$db->quoteName('id') . ' = ' . $obj);


	             $query->delete($db->quoteName('#__device_table'));
	             $query->where($conditions);

	             $db->setQuery($query);
	             $db->execute();

		}



		public static function getDIODevices($name='')
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		    $query
		    ->select('DISTINCT a.*');

		    $query->from('`#__top_table` AS a');

		    $query->where('a.state = 1');

				$query->where('a.note1 = '.self::$dpage);

        if($name != '')
				    $query->where('a.name LIKE "'.$name.'"');

		    $db->setQuery($query);

        //echo($query);
		    $items = (array) $db->loadObjectList();

		    return $items;
		}

		public static function getDIODevice($obj)
		{

			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		    $query
		    ->select('DISTINCT a.*');

		    $query->from('`#__device_table` AS a');

		    $query->where('a.state = 1');

        //$query->where('a.dio_id > 0 ');

        $query->where($db->quoteName('a.dio_id') . ' = ' . $db->quote($obj->ipaddr));

				//$query->where('a.info = "'.$obj->ipaddr .'"');
				$query->where('a.index = '.$obj->index);

        $db->setQuery($query);

        //echo($query);

				$items = (array) $db->loadObjectList();

		    return $items;

		}


		public static function getDIODeviceById($id)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		    $query
		    ->select('DISTINCT a.*');

		    $query->from('`#__device_table` AS a');

		    $query->where('a.state = 1');

				$query->where('a.id = '.$id);

        $db->setQuery($query);

        //echo($query);
		    $items = (array) $db->loadObjectList();

		    return $items;
		}

    public static function setbgcolor($item)
		{
        $color = "white";
				if($item->status == 1)
				{
					$color = "Violet";
				}
				else if($item->status == 2)
				{
					$color = "DodgerBlue";
				}

				return $color;
		}
    public static function is_checked($item)
		{
			  $status = false;
		    if($item->status == 0 ||
				   $item->status == 2
				)
				{
					$status = true;
				}

				return $status;
		}
		public static function getDevicePath($obj)
		{
        $arr = array();
        foreach($obj->id as $i => $item)
				{
					$lists = self::getDevice($item);
					if(count($lists))
					{
						if(empty($lists[0]->green))
						{
							$mygroups = self::getGroupItem($lists[0]->group);
							if(count($mygroups))
							{
								$lists[0]->green = $mygroups[0]->green;
								$lists[0]->red = $mygroups[0]->red;
								$lists[0]->yellow = $mygroups[0]->yellow;
							}
						}
              array_push($arr,$lists[0]);
					}
				}

				$obj->arr = $arr;
		}
		public static function getIfUserSuppressedAutoRedirect($user)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
			$query
			->select('DISTINCT site_settings.*');

			$query->from('`#__site_settings` AS site_settings');

			$query->where('site_settings.state = 1');

			$query->where('site_settings.name = '. $db->quote('GROUP_SETTINGS'));

			$db->setQuery($query);

			$items = (array) $db->loadObjectList();
			if (count($items) > 0)
			{
				$group_settings = json_decode($items[0]->content);
				foreach ($group_settings->groups as $key => $group) {
					if ($group->suppress_auto_redirect)
					{
						if (in_array($group->id, $user->groups))
						{
							return true;
						}
					}
				}
				return false;
			}
			else
			{
				return false;
			}

		}
		public static function getTimingSets()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->select('*')
			->from('`#__timing_set` AS timing')
			->where('timing.enabled =1');
			$db->setQuery($query);
			$timing_sets = (array) $db->loadObjectList();
			foreach ($timing_sets as $key => $timing_set) {
				$timing_sets[$key]->supressed_alarm_nodes = explode(",", $timing_sets[$key]->supressed_alarm_nodes);
				$timing_sets[$key]->weekdays = explode(",", $timing_sets[$key]->weekdays);
				$timing_sets[$key]->start_hour = (int)substr($timing_set->start_time, 0 , 2);
				$timing_sets[$key]->start_minute = (int)substr($timing_set->start_time, 2 , 2);
				$timing_sets[$key]->end_hour = (int)substr($timing_set->end_time, 0 , 2);
				$timing_sets[$key]->end_minute = (int)substr($timing_set->end_time, 2 , 2);
			}
			return $timing_sets;
		}
		public static function getDioSupressedAlarmNodes()
		{
			// $minute = date('i');
			$today = new DateTime('now',new DateTimeZone('Asia/Taipei'));
			$weekday = (int)$today->format('N');

			$hour = (int)$today->format('H');
			$minute = (int)$today->format('i');
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
				$query->select('*')
				->from('`#__timing_set` AS timing')
				->where('timing.enabled =1')
				->where('FIND_IN_SET(0,' . $db->quoteName('timing.weekdays') .') OR FIND_IN_SET('. $db->quote($weekday). ',' . $db->quoteName('timing.weekdays') .')');

				// ->where('FIND_IN_SET(' .$db->quote($id). ", " . $db->quoteName('supressed_alarm_nodes') . ')');
				// $query->where('FIND_IN_SET(' . $db->quote($filter_weekdays) . ', ' . $db->quoteName('a.weekdays') . ')');
			$db->setQuery($query);
			$all_items = (array) $db->loadObjectList();
			$items = array_filter($all_items, function($item) use ($hour,$minute) {
				$start_hour = (int)substr($item->start_time,0,2);
				$start_minute = (int)substr($item->start_time,2,2);

				$end_hour = (int)substr($item->end_time,0,2);
				$end_minute = (int)substr($item->end_time,2,2);
					return 	(
						$hour > $start_hour ||
						($hour == $start_hour && $minute >= $start_minute)
						)
						&&
						(
							$hour < $end_hour ||
							($hour == $end_hour && $minute < $end_minute)
						);
			});


			// return count($items) == 0;
			$nodes = array();
			if (count($items) > 0)
			{
				foreach($items as $key => $timing)
				{
					if (strlen($timing->supressed_alarm_nodes) > 0)
					{
						$timing_nodes = explode(",", $timing->supressed_alarm_nodes);
						foreach ($timing_nodes as $key1 => $node_id) {
							array_push($nodes, $node_id);
						}
					}
				}
				return array_unique($nodes);
			}
			return array();
		}
		public static function getDiskSpace()
		{
			try {
				return disk_free_space(".");
			} catch (\Throwable $th) {
				return -1;
			}
		}
		public function HumanSize($Bytes)
		{
			$Type=array("bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB");
			$Index=0;
			while($Bytes>=1024)
			{
				$Bytes/=1024;
				$Index++;
			}
			$Bytes = round($Bytes,2);
			return("".$Bytes." ".$Type[$Index]);
		}
		public static function getDbSize()
		{
			try {
				$db = JFactory::getDbo();
				$sql = "SELECT table_schema ,
					SUM(data_length + index_length) AS size
					FROM information_schema.tables
					WHERE table_schema = 'joomladb'
					GROUP BY table_schema;";
				$db->setQuery($sql);
				$result =  (array) $db->loadObjectList();
				if (count($result) > 0)
				{
					return $result[0]->size;
				}
				return 0;
			} catch (\Throwable $th) {
				return -1;
			}

		}
		public static function getDioTriggerAlarmStatus($id)
		{
			$today = new DateTime('now',new DateTimeZone('Asia/Taipei'));
			$weekday = (int)$today->format('N');
			$hour = (int)$today->format('H');
			$minute = (int)$today->format('i');
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
				$query->select('*')
				->from('`#__timing_set` AS timing')
				->where('timing.enabled =1 AND (FIND_IN_SET(0,' . $db->quoteName('timing.weekdays') .') OR FIND_IN_SET('. $db->quote($weekday). ',' . $db->quoteName('timing.weekdays') .')) AND FIND_IN_SET(' .$db->quote($id). ", " . $db->quoteName('supressed_alarm_nodes') . ')');
				// ->where('FIND_IN_SET(0,' . $db->quoteName('timing.weekdays') .') OR FIND_IN_SET('. $db->quote($weekday). ',' . $db->quoteName('timing.weekdays') .')')
				// ->where('FIND_IN_SET(' .$db->quote($id). ", " . $db->quoteName('supressed_alarm_nodes') . ')');
				// $query->where('FIND_IN_SET(' . $db->quote($filter_weekdays) . ', ' . $db->quoteName('a.weekdays') . ')');
			$db->setQuery($query);
			// return $query->__toString();
			$all_items = (array) $db->loadObjectList();
			if (count($all_items) == 0)
			{
				return true;
			}
			$result = array_filter($all_items, function($item) use ($hour,$minute) {
				$start_hour = (int)substr($item->start_time,0,2);
				$start_minute = (int)substr($item->start_time,2,2);

				$end_hour = (int)substr($item->end_time,0,2);
				$end_minute = (int)substr($item->end_time,2,2);
				// return true;
					return 	(
						$hour > $start_hour ||
						($hour == $start_hour && $minute >= $start_minute)
						)
						&&
						(
							$hour < $end_hour ||
							($hour == $end_hour && $minute < $end_minute)
						);
			});
			// return $result;
			// return false;
			return count($result) == 0;
		}
		public static function setDIOStatus2($obj)
		{

			$db = JFactory::getDbo();
			$items1 = self::getDIODeviceById($obj->id);

      $fields1 = array($db->quoteName('open') . ' = ' . $db->quote(0));

      $open = 0;
      if($obj->status == 3 && $items1[0]->trigger_alarm)
          $open = 1;

			if(count($items1) > 0 && $items1[0]->enable == 1 && $items1[0]->trigger_alarm && self::getDioTriggerAlarmStatus($obj->id))
			{
					//echo('action '.$obj->dio_alarm);
					$items2 = self::get_dio_top($items1[0]->dio_alarm);

					if(count($items2))
					{

            foreach(range(0,3) as $i)
						{
						    $fields1 = TopHelpersUtility::set_open_path($items2[0],$db,$open,$i,$fields1);
            }
/*
						if($items2[0]->cctv_timeout == 0)
								$items2[0]->cctv_timeout = 60;

						system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$items2[0]->cctv_timeout." > /dev/null 2> /dev/null &");
*/
					}
			}

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			  $fields = array(
					$db->quoteName('status') . ' = ' . $db->quote($obj->status),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time)

				);
				$last_alarm_time = new stdClass();
				if ($items1[0]->status == 3 && $obj->status == 1)
				{
					$update1 = str_replace("/", "-", $update);;

					$last_alarm_time = $update1 . " " . $update_time;
					$fields = array_merge($fields, array(
						$db->quoteName('last_alarm_time') . ' = ' . $db->quote($last_alarm_time),
					));
				}
        $fields = array_merge($fields,$fields1);
				$conditions = array(
					  //$db->quoteName('dio_id') . ' > 0',
					  //$db->quoteName('index') . ' = ' . $db->quote($obj->index),
						$db->quoteName('id') . ' = ' . $db->quote($obj->id)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

        //echo($query);
				$items1 = self::getDIODeviceById($obj->id);

        if(count($items1) > 0 && $items1[0]->enable == 1)
				{
            //self::check_alarm_action($items1[0]);
						$myitems = self::getDIOAlarm($items1[0]->dio_alarm,$items1[0]->status);

						self::send_alarm_action2($myitems);
						//self::sendMessage($items1);
            self::add_to_devicelog($items1[0]);

			self::add_to_node_history($items1[0]->id, $items1[0]->status);

        }

		}

		public static function setDIOStatus($obj)
		{
			$db = JFactory::getDbo();
			$items1 = self::getDIODevice($obj);

      $fields1 = array();

      $open = 0;
      if($obj->status == 3)
          $open = 1;
			if(count($items1) > 0 && $items1[0]->enable == 1)
			{
					//echo('action '.$obj->dio_alarm);
					$items2 = self::get_dio_top($items1[0]->dio_alarm);

					if(count($items2))
					{
						foreach(range(0,3) as $i)
						{
								$fields1 = TopHelpersUtility::set_open_path($items2[0],$db,$open,$i,$fields1);
						}

						if($items2[0]->isvideo)
						{
							if($open == 1)
							{
/*
                if($items2[0]->cctv_timeout == 0)
								    $items2[0]->cctv_timeout = 60;

                system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$items2[0]->cctv_timeout." > /dev/null 2> /dev/null &");
*/
							}

						}

						//self::do_alarm_timeout($items2);

					}
			}

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			  $fields = array(
					$db->quoteName('status') . ' = ' . $db->quote($obj->status),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time)

				);

        $fields = array_merge($fields,$fields1);
				$conditions = array(
					  //$db->quoteName('dio_id') . ' > 0',
					  $db->quoteName('index') . ' = ' . $db->quote($obj->index),
						$db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

        //echo($query);
				$items1 = self::getDIODevice($obj);

        if(count($items1) > 0 && $items1[0]->enable == 1)
				{

					  self::sendMessage($items1);
            self::add_to_devicelog($items1[0]);
						self::check_alarm_action($items1[0]);

        }

		}
		public static function add_to_node_history($id,$status)
		{
			$db = JFactory::getDbo();
			$columns = array('device_id','status');
			$values = array($db->quote($id),$db->quote($status != 1));
			$query = $db->getQuery(true);
			$query->insert($db->quoteName('#__node_history'))
				->columns($db->quoteName($columns))
				->values(implode(',', $values));
			$db->setQuery($query)->execute();

		}
		public static function updateConditionDIOStatus($obj)
		{
			$db = JFactory::getDbo();

			if($obj->value == 1)
			{
				$status = 3;
			}
			else if($obj->value == 0)
			{
				$status = 1;
			}
			else
			{
				return;
			}


			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			  $fields = array(
					$db->quoteName('status') . ' = ' . $db->quote($status),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time)

				);

        //$fields = array_merge($fields,$fields1);
				$conditions = array(
					  //$db->quoteName('dio_id') . ' > 0',
					  $db->quoteName('index') . ' = ' . $db->quote($obj->index),
						$db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr)
				);

	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();


		}

		public static function setCondDIOStatus2($obj)
		{

			$db = JFactory::getDbo();

      $fields1 = array();

      $open = 0;
      if($obj->status == 3)
          $open = 1;

      $cctv_timeout = 60;
			$isvideo = false;
			//echo('action '.$obj->dio_alarm);
			$items2 = self::get_dio_top($obj->group);

			 if(count($items2))
				{
						//echo("action 2 ".$items2[0]->isvideo);
						if($items2[0]->isvideo)
						{
							foreach(range(0,3) as $i)
							{
							    $fields1 = TopHelpersUtility::set_open_path($items2[0],$db,$open,$i,$fields1);
	            }


              $isvideo = true;
							$cctv_timeout = $items2[0]->cctv_timeout;
						}

					}

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			  $fields = array(
					$db->quoteName('status') . ' = ' . $db->quote($obj->status),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time)

				);

        $fields = array_merge($fields,$fields1);
				$conditions = array(
					  //$db->quoteName('dio_id') . ' > 0',
					  $db->quoteName('index') . ' = ' . $db->quote($obj->index),
						$db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

        //echo($query);
				$items1 = self::getDIODevice($obj);

        if(count($items1) > 0 && $items1[0]->enable == 1)
				{
					 // self::sendMessage($items1);
						if($isvideo)
						{
							///system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$cctv_timeout." > /dev/null 2> /dev/null &");
						}
            //self::add_to_devicelog($items1[0]);
						//self::check_alarm_action($items1[0]);

        }

		}
		public static function setCondDIOStatus($obj)
		{
			$db = JFactory::getDbo();

      $fields1 = array();

      $open = 0;
      if($obj->status == 3)
          $open = 1;

      $cctv_timeout = 60;
			$isvideo = false;
			//echo('action '.$obj->dio_alarm);
			$items2 = self::get_dio_top($obj->group);

			 if(count($items2))
				{
						//echo("action 2 ".$items2[0]->isvideo);
						if($items2[0]->isvideo)
						{
							foreach(range(0,3) as $i)
							{
									$fields1 = TopHelpersUtility::set_open_path($items2[0],$db,$open,$i,$fields1);
							}


              $isvideo = true;
							$cctv_timeout = $items2[0]->cctv_timeout;
						}

					}

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			  $fields = array(
					$db->quoteName('status') . ' = ' . $db->quote($obj->status),
					$db->quoteName('update') . ' = ' . $db->quote($update),
					$db->quoteName('update_time') . ' = ' . $db->quote($update_time)

				);

        $fields = array_merge($fields,$fields1);
				$conditions = array(
					  //$db->quoteName('dio_id') . ' > 0',
					  $db->quoteName('index') . ' = ' . $db->quote($obj->index),
						$db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr)
				);
	      $query = $db->getQuery(true);
	      $query->update($db->quoteName('#__device_table'))->set($fields)->where($conditions);

		    $db->setQuery($query)->execute();

        //echo($query);
				$items1 = self::getDIODevice($obj);

        if(count($items1) > 0 && $items1[0]->enable == 1)
				{
					  self::sendMessage($items1);
						if($isvideo)
						{
							///system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$cctv_timeout." > /dev/null 2> /dev/null &");
						}
            //self::add_to_devicelog($items1[0]);
						//self::check_alarm_action($items1[0]);

        }

        self::send_cond_alarm_action($obj);

		}
		public static function do_alarm_timeout($items)
    {
			if(count($items) > 0)
			{
				if($items[0]->iscaller)
				{
					$timeout_alarm = $items[0]->timeout_alarm;
					if($timeout_alarm == 0)
					{
						  $obj = new stdClass;

							$obj->group = $items[0]->id;
							self::alarm_timeout($obj);
					}
					else
							system('/bin/bash /opt/24dio/alarm_timeout.sh '.$items[0]->id." ".$items[0]->timeout_alarm." > /dev/null 2> /dev/null &");
				}
			}
    }
		public static function send_alarm_action2($items)
    {
			if(count($items) == 0)    return;

			foreach($items as $i => $item)
			{
				$item->check_name = '警報';
				$item->type = $item->check_name;

         self::add_dio_log($item);
  		}
    }
		public static function send_alarm_action($items,$status,$items1)
    {
			self::do_alarm_timeout($items1);

			if(count($items) == 0)    return;

			$desc = '';

			foreach($items as $i => $item)
			{
      		$desc = 'ACTION=device '.$item->device.' '.$item->index.' '.$item->value. ' '.$item->timeout."\n".$desc;
			}

			self::write_to_socket($desc);

			foreach($items as $i => $item)
			{
				$item->check_name = '警報';
				$item->type = $item->check_name;

         self::add_dio_log($item);
  		}
    }

		public static function add_dio_log($item)
		{
			$item->end_status = $item->value;
			if($item->timeout > 0)
			{
				$item->end_status = 0;
				if($item->value == 0)
						$item->end_status = 1;
			}

      $dio_dev = new stdClass;
			$dio_dev->ipaddr = $item->device;
			$dio_dev->index = $item->index;
			$objs = self::getDIODevice($dio_dev);

			if(count($objs))
			{
				  $objs[0]->check_name = $item->check_name;
					$objs[0]->type = $item->type;
					$objs[0]->end_status = $item->end_status;
					$objs[0]->status = $item->value+1;
					$objs[0]->check_date = date("Y/m/d");
					$objs[0]->check_time = date("H:i:s");
					self::do_add_to_devicelog($objs[0]);
			}

		}
		public static function alarm_timeout($obj)
		{
			$items1 = self::getThisGroupItem($obj->group);

			if(count($items1) > 0)
			{
				if($items1[0]->iscaller)
				{
					$callee = $items1[0]->caller;
					$mymsg = array();
					$obj1 = new stdClass;

          $obj1->callee = $items1[0]->callee;
					array_push($mymsg,$obj1);

					self::do_sendAlarmMessage($mymsg,$callee);

				}
			}
		}


		public static function send_cond_alarm_action($obj)
		{
			  $items = self::getDIOAlarm($obj->group,$obj->status);

				if(count($items) == 0)    return;

        $items1 = self::getThisGroupItem($obj->group);

        self::do_alarm_timeout($items1);
				$desc = '';

				foreach($items as $i => $item)
				{
	      		$desc = 'ACTION=device '.$item->device.' '.$item->index.' '.$item->value. ' '.$item->timeout."\n".$desc;
				}

				self::write_to_socket($desc);

				foreach($items as $i => $item)
				{
					$item->check_name = '條件警報';
					$item->type = $item->check_name;

          self::add_dio_log($item);
				}
		}
		public static function send_timing_action2($obj)
		{
				$items = self::getDIOAlarm($obj->group,$obj->status);

				if(count($items) == 0)    return;

				foreach($items as $i => $item)
				{
					$item->check_name = '時序';
					$item->type = $item->check_name;

					self::add_dio_log($item);
				}
		}
		public static function send_timing_action($obj)
		{
				self::do_alarm($obj);
				$obj->group = $obj->alarm;
			/*
				$items = self::getDIOAlarm($obj->group,$obj->status);

				if(count($items) == 0)    return;

				$desc = '';

				foreach($items as $i => $item)
				{
						$desc = 'ACTION=device '.$item->device.' '.$item->index.' '.$item->value. ' '.$item->timeout."\n".$desc;
				}

				self::write_to_socket($desc);
*/
        $ips = TopHelpersUtility::getBaList();
				foreach($ips as $i=>$ip)
				{
					$cmd = 'curl https://'.$ip->url.'/index.php?option="com_floor&task=sroots.timing2&value='.$obj->group.'"'.' -k --connect-timeout 3 -o  /tmp/t > /dev/null 2> /dev/null &';
          			system($cmd);
				}
		}
		public static function check_alarm_action($obj)
		{
        //if($obj->status != 3)    return;

			  $items = self::getDIOAlarm($obj->dio_alarm,$obj->status);

        //echo('action '.$obj->dio_alarm);
				$items1 = self::get_dio_top($obj->dio_alarm);

				self::send_alarm_action($items,$obj->status,$items1);

		}

		public static function reset_cctv($id)
    {
			//echo('updateDevicesByIpaddr '.$ipaddr);
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

      $open = 0;

			$db = JFactory::getDbo();

			$query = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))

			 //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
			 ->set($db->quoteName('open') . ' = ' . $db->quote($open))
			 ->set($db->quoteName('open1') . ' = ' . $db->quote($open))
			 ->set($db->quoteName('open2') . ' = ' . $db->quote($open))
			 ->set($db->quoteName('open3') . ' = ' . $db->quote($open))

			 ->set($db->quoteName('update') . ' = ' . $db->quote($update))
			 ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

				->where($db->quoteName('id') . ' = ' . $db->quote($id));

				$db->setQuery($query)->execute();
        //echo($query);

    }

		public static function get_dio_top($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			// Select the required fields from the table.
		 $query
		 ->select('DISTINCT a.*');

		 $query->from('`#__top_table` AS a');

		 $query->where('a.state = 1');

		 $query->where('a.note1 = '.self::$apage);

		 //$query->where('a.iscondition > 0');

		 $query->where('a.id = '.$id);

		 $db->setQuery($query);

		 $items = (array) $db->loadObjectList();

		 return $items;
	 }
		public static function getCCTVGroupItem($mygroup)
		{
			  // Create a new query object.
			  $db = JFactory::getDbo();
			  $query = $db->getQuery(true);

			  // Select the required fields from the table.
		   $query
		       ->select('DISTINCT a.*');

		   $query->from('`#__top_table` AS a');

		   $query->where('a.state = 1');
		   $query->where('a.note1 = '.self::$page);
			 $query->where('a.type = 3');

       if($mygroup > 0)
			     $query->where('a.id = '.$mygroup);

		   $db->setQuery($query);

		   $items = (array) $db->loadObjectList();

		   return $items;

		}

		public static function is_phone($obj)
    {
		    if($obj->dio_id == 0 && $obj->type == 1)    return true;
			return false;

    }
		public static function is_cctv($obj)
		{
			  if($obj->dio_id == 0 && $obj->type == 3)    return true;

				return false;

		}

		public static function send_to_center($obj)
		{
        //$obj->at_value = 0;
				$ips = self::getRelayList();

				foreach($ips as $i=>$ip)
				{
            $cmd = '/index.php?option="com_floor&task=sroots.'.$obj->action.'&parent='.$obj->parent_id.'&dio_id='.$obj->dio_id.'&index='.$obj->index.'&value='.$obj->at_value.'&timeout='.$obj->timeout."\" -k --connect-timeout  3 -o /tmp/DO_ACTION  > /dev/null 2> /dev/null &";
            $cmd = 'curl https://'.$ip->url.$cmd;
						system($cmd);
				    echo($cmd);
			  }
		}
		public static function phoneAlarm2($obj)
		{
			$desc = 'PHONEALARM=device '.$obj->number.$obj->msg." ".$obj->status;

			self::write_to_socket2($desc);

			$msgs = TopHelpersUtility::findAlarmMsg($obj->number.$obj->msg);

			$str = 'alarm';
			if(count($msgs))
			{
				$str = $msgs[0]->note;
			}

			$sos_numbers = TopHelpersUtility::findSOSNumber($obj->number);

			if(count($sos_numbers) == 0)    return;

			$desc = $str;

            $callee =  $sos_numbers[0]->ip;

			$in = 'desc='.$desc.'&callee='.$callee;//input, transfer msg to server side

			self::do_write_to_socket($in);


		}
		public static function sendToUrl($obj)
    {
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($obj->arr));
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

			foreach($obj->ips as $i=>$ip)
			{
				$url = "http://".$ip->url.'/index.php';

				curl_setopt($ch, CURLOPT_URL, $url);

				$output = curl_exec($ch);

				$file = fopen($obj->filename,"w");
				fwrite($file,$output);
				fclose($file);

			}

			curl_close($ch);

    }


		public static function do_alarm($obj)
		{
			$desc = 'ALARM=device '.$obj->alarmdir." ".$obj->alarm;

	    self::write_to_socket2($desc);
		}

		public static function reload2()
		{
			$desc = 'RELOAD=device ';

	    self::write_to_socket2($desc);
		}
		public static function setDOAction2($obj)
		{
			$desc = 'ACTION=device '.$obj->parent_id.' '.$obj->dio_id.' '.$obj->index.' '.$obj->value.' '.$obj->timeout;

	    self::write_to_socket2($desc);
		}
		public static function setDOAction1($obj)
		{
        $items = self::getDIODeviceById($obj->id);
				if(count($items) == 0)
        {
					  echo('empty');
				    return;
        }

        $mytop = self::getThisGroupItem($items[0]->dio_id);

				if(count($mytop) == 0)
				{
					echo('np top');
					return;
				}

				$myparent = self::getThisGroupItem($mytop[0]->parent);

				$vendor = 0;

        if(count($myparent) == 0)
				{
					echo('np parent');
					return;
				}

        $items[0]->parent_id = $myparent[0]->id;

        $items[0]->action = 'do_action2';
				$items[0]->timeout = 0;

				$items[0]->at_value = 0;
				if($items[0]->dio_value == 0)
				{
					$items[0]->at_value = 1;

				}

        //return;
				self::send_to_center($items[0]);

				$items[0]->type = "手動";
				$items[0]->status = $items[0]->at_value+1;
				$items[0]->end_status = $items[0]->at_value;

				$items[0]->check_name = $obj->check_name;
				$items[0]->check_date = date("Y/m/d");
				$items[0]->check_time = date("H:i:s");

				self::do_add_to_devicelog($items[0],2);
    }

		public static function write_to_socket2($desc)
		{
			$len = strlen($desc);
			$hex = sprintf('%08X', $len);
			$desc = "WA=".$hex.$desc;

			self::do_write_to_socket2($desc);
		}
    public static function write_to_socket($desc)
		{
			$len = strlen($desc);

			$hex = sprintf('%08X', $len);
			$desc = "WA=".$hex.$desc;

			self::do_write_to_socket($desc);
		}
		public static function setDOAction($obj)
		{
        $items = self::getDIODeviceById($obj->id);
				if(count($items) == 0)
        {
					  echo('empty');
				    return;
        }

        $mytop = self::getThisGroupItem($items[0]->dio_id);

				if(count($mytop) == 0)
				{
					echo('np top');
					return;
				}

				$vendor = 0;

				$vendor = $mytop[0]->vendor;

        if($vendor == self::$SOYAL_VENDOR)
				{
					$value = 1;
					$desc = 'OPEN=device '.$items[0]->dio_id.' '.$items[0]->id.' '.$value.' 0';

				}
				else
				{
            $value = 0;
				    if(($items[0]->dio_value) == 0)
				    {
						    $value = 1;
				    }

				    $desc = 'ACTION=device '.$items[0]->dio_id.' '.$items[0]->index.' '.$value.' 0';
				    //echo($desc);
        }

        self::write_to_socket2($desc);

				$items[0]->type = "手動";
				$items[0]->status = $value+1;
				$items[0]->end_status = $value;

				$items[0]->check_name = $obj->check_name;
				$items[0]->check_date = date("Y/m/d");
				$items[0]->check_time = date("H:i:s");

				self::do_add_to_devicelog($items[0],2);
    }

		public static function phoneCalloLog($item)
		{
			$item->type = "強制撥出";

      $item->status = 1001;
			$item->end_status = $item->status;

			$item->check_name = $item->check_name;
			$item->check_date = date("Y/m/d");
			$item->check_time = date("H:i:s");

			self::do_add_to_devicelog($item,2);
		}

    public static function phoneCCTVToLog($item)
		{
			$item->type = "phone";

			$item->end_status = $item->status;

			$item->check_name = "sip server";
			$item->check_date = date("Y/m/d");
			$item->check_time = date("H:i:s");
			$item->need_alarm = 1;
			self::do_add_to_devicelog($item,2);
		}
		public static function do_write_to_socket($str)
		{

      //echo $str. "<br>";
			$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

			if ($socket < 0) {
				  JLog::add("---Failed: socket_create() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');
			    //echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
          echo("\n1");
					return;
			}

			//socket_connect
			$result = socket_connect($socket, "/tmp/phpsocket");

			if ($result < 0) {
				  JLog::add("---Failed: socket_connect() failed! Reason: " . socket_strerror($result), JLog::INFO, 'jerror');

			   // echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

         echo("\n2");
	        //Close
	        socket_close($socket);
					return;
			}

			$in = $str;//input, transfer msg to server side

			//socket_write
			if(!socket_write($socket, $in, strlen($in))) {
				  JLog::add("---Failed: socket_write() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');
          echo("\n3");
			    //echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
			}


			//Close
			socket_close($socket);

		}

		public static function do_write_to_socket2($str)
		{

      //echo $str. "<br>";
			$socket = socket_create(AF_UNIX, SOCK_STREAM, 0);

			if ($socket < 0) {
				  JLog::add("---Failed: socket_create() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');
			    //echo "---Failed: socket_create() failed! Reason: " . socket_strerror($socket) . "<br>";
          echo("\n");
					return;
			}

			//socket_connect
			$result = socket_connect($socket, "/tmp/diosocket2");

			if ($result < 0) {
				  //JLog::add("---Failed: socket_connect() failed! Reason: " . socket_strerror($result), JLog::INFO, 'jerror');

			    echo "---Failed: socket_connect() failed! Reason: " . socket_strerror($result) . "<br>";

         echo("\n");
	        //Close
	        socket_close($socket);
					return;
			}

			$in = $str;//input, transfer msg to server side

			//socket_write
			if(!socket_write($socket, $in, strlen($in))) {
				  //JLog::add("---Failed: socket_write() failed! Reason: " . socket_strerror($socket), JLog::INFO, 'jerror');
				echo($in);
				echo("\n");
			    echo "---Failed: socket_write() failed! Reason: " . socket_strerror($socket) . "\n";
			}


			//Close
			socket_close($socket);

		}

		public static function dio_condition2($obj)
		{
			  $items = self::getDIODevice($obj);

			  if(count($items) == 0)    return;

        self::updateConditionDIOStatus($obj);

        $obj->value = 100+$obj->value;
				echo(" ".$obj->value." ");
        if($obj->value == (100+1))
				{
					  $items[0]->status = $obj->value;
						$items[0]->is_need_alarm = 1;
					  self::do_add_to_devicelog($items[0],$items[0]->status);
				}
				else
				{
					  if($obj->value == 100)
						{
							$obj->status = 0;
						}
						else
						    $obj->status = $obj->value;
					  $obj->condition = 1;
						$mylog = self::findDevicelog($items[0],1);
						if(count($mylog))
		            self::update_devicelog($mylog[0],$obj);
				}

		}

		public static function dio_condition($obj)
		{
			  $items = self::getDIODevice($obj);
			  if(count($items) == 0)    return;

        self::updateConditionDIOStatus($obj);

        $obj->value = 100+$obj->value;
				echo(" ".$obj->value." ");
        if($obj->value == (100+1))
				{
					  $items[0]->status = $obj->value;
					  self::do_add_to_devicelog($items[0],$items[0]->status);
				}
				else
				{
					  if($obj->value == 100)
						{
							$obj->status = 0;
						}
						else
						    $obj->status = $obj->value;
					  $obj->condition = 1;
						$mylog = self::findDevicelog($items[0],1);
						if(count($mylog))
		            self::update_devicelog($mylog[0],$obj);
				}

		}
		public static function updateCCTVOpen($obj,$open)
		{
			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

			$db = JFactory::getDbo();

			$query = $db->getQuery(true)
					->update($db->quoteName('#__device_table'))

			 //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
			 ->set($db->quoteName('open') . ' = ' . $db->quote($open))
       ->set($db->quoteName('path') . ' = ' . $db->quote($obj->path))

			 ->set($db->quoteName('open1') . ' = ' . $db->quote($open))
       ->set($db->quoteName('path1') . ' = ' . $db->quote($obj->path1))

			 ->set($db->quoteName('open2') . ' = ' . $db->quote($open))
       ->set($db->quoteName('path2') . ' = ' . $db->quote($obj->path2))

			 ->set($db->quoteName('open3') . ' = ' . $db->quote($open))
       ->set($db->quoteName('path3') . ' = ' . $db->quote($obj->path3))

			 ->set($db->quoteName('update') . ' = ' . $db->quote($update))
			 ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

				->where($db->quoteName('info') . ' = ' . $db->quote($obj->path));

				$db->setQuery($query)->execute();
        //echo($query);

        if($open == 1)
				//if(false)
				{
					  $items1 = self::getIPDevicesByIpaddr($obj->path);
						if(count($items1))
				        ;///system('/bin/bash /opt/24dio/cctv_timeout.sh '.$items1[0]->id." ".$obj->cctv_timeout." > /dev/null 2> /dev/null &");
			  }

		}
		public static function updateSOYAL($obj)
		{
			$status = 1;

	    if($obj->value == 1)
			{
				$status = 2;
			}

			$update = date ("Y/m/d");
		  $update_time = date ("H:i:s");

				$db = JFactory::getDbo();

				$query = $db->getQuery(true)
				    ->update($db->quoteName('#__device_table'))

				  //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
				  ->set($db->quoteName('status') . ' = ' . $db->quote($status))

				  ->set($db->quoteName('update') . ' = ' . $db->quote($update))
				  ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))
				  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->id));

          $db->setQuery($query)->execute();

					//return;
					$items = self::getDIODeviceById($obj->id);
					if(count($items) == 0)    return;

					$open = 0;
		      if($status == 2)
		          $open = 1;
					if(count($items) > 0 && $items[0]->enable == 1)
					{
							$items2 = self::get_dio_top($items[0]->dio_alarm);

							if(count($items2))
							{
								if($items2[0]->isvideo)
								{
                  self::updateCCTVOpen($items2[0],$open);

								}

								self::do_alarm_timeout($items2);

							}
					}

					$mylog = self::findDevicelog($items[0],1);

					if(count($mylog) && ($mylog[0]->status == 1 || $mylog[0]->status == 2))
					{
						  echo(" ".$mylog[0]->end_status." ".$obj->value." ".$items[0]->status." ");
							if($status == 1)
							{
									$items[0]->status = 1;
									self::update_devicelog($mylog[0],$items[0]);
							}
							else
							{
							    echo("not equ");
							}
					}
					else
					{
						  if($status == 2)
						  {
								   $items[0]->check_name = '';
								   $items[0]->type = $items[0]->note;
								   $items[0]->end_status = 1;
								   $items[0]->status = 107;
								   $items[0]->check_date = date("Y/m/d");
								   $items[0]->check_time = date("H:i:s");
								   self::do_add_to_devicelog($items[0]);
						}

					}

		 }
		 public static function updateDO2($obj)
 		{
 			$status = 1;

 	    if($obj->value)
 			{
 				$status = 2;
 			}

 			$update = date ("Y/m/d");
 		  $update_time = date ("H:i:s");

 				$db = JFactory::getDbo();

 				$query = $db->getQuery(true)
 				    ->update($db->quoteName('#__device_table'))

 				 //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
 				 ->set($db->quoteName('status') . ' = ' . $db->quote($status))
 				 ->set($db->quoteName('dio_value') . ' = ' . $db->quote($obj->value))
 				 ->set($db->quoteName('update') . ' = ' . $db->quote($update))
 				 ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

 				 //->where('dio_id > 0')
 		      //->where($db->quoteName('id') . ' = ' . $db->quote($obj->index))
 				  ->where($db->quoteName('id') . ' = ' . $db->quote($obj->ipaddr));

           //echo($query);

 				  $db->setQuery($query)->execute();

           //return;
           $items = self::getDIODevice($obj);
 					if(count($items) == 0)    return;

 					$mylog = self::findDevicelog($items[0],1);

 					if(count($mylog) && ($mylog[0]->status == 1 || $mylog[0]->status == 2))
 					{
 						  echo(" ".$mylog[0]->end_status." ".$obj->value." ".$items[0]->status." ");
 						  if($mylog[0]->end_status == $obj->value)
 							{
                   $items[0]->condition = 0;
 		              self::update_devicelog($mylog[0],$items[0],1);
               }
 							else
 							{
 							    echo("not equ");
 							}
 					}
 					else
 					{
 						   echo("empty");
 					}

 		 }
		 public static function updateDO($obj)
 		{
 			$status = 1;

 	    if($obj->value)
 			{
 				$status = 2;
 			}

 			$update = date ("Y/m/d");
 		  $update_time = date ("H:i:s");

 				$db = JFactory::getDbo();

 				$query = $db->getQuery(true)
 				    ->update($db->quoteName('#__device_table'))

 				 //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
 				 ->set($db->quoteName('status') . ' = ' . $db->quote($status))
 				 ->set($db->quoteName('dio_value') . ' = ' . $db->quote($obj->value))
 				 ->set($db->quoteName('update') . ' = ' . $db->quote($update))
 				 ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

 				 //->where('dio_id > 0')
 		      ->where($db->quoteName('index') . ' = ' . $db->quote($obj->index))
 				  ->where($db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr));

           //echo($query);

 				  $db->setQuery($query)->execute();

           //return;
           $items = self::getDIODevice($obj);
 					if(count($items) == 0)    return;

 					$mylog = self::findDevicelog($items[0],1);

 					if(count($mylog) && ($mylog[0]->status == 1 || $mylog[0]->status == 2))
 					{
 						  echo(" ".$mylog[0]->end_status." ".$obj->value." ".$items[0]->status." ");
 						  if($mylog[0]->end_status == $obj->value)
 							{
                   $items[0]->condition = 0;
 		              self::update_devicelog($mylog[0],$items[0],1);
               }
 							else
 							{
 							    echo("not equ");
 							}
 					}
 					else
 					{
 						   echo("empty");
 					}

 		 }
		 public static function updateDI($obj)
 		{
 			$status = 1;

 	    if($obj->value)
 			{
 				$status = 2;
 			}

 			$update = date ("Y/m/d");
 		  $update_time = date ("H:i:s");

 				$db = JFactory::getDbo();

 				$query = $db->getQuery(true)
 				    ->update($db->quoteName('#__device_table'))

 				 //->set($db->quoteName('serial') . ' = ' . $db->quote($obj->serial))
 				 ->set($db->quoteName('status') . ' = ' . $db->quote($status))
 				 //->set($db->quoteName('dio_value') . ' = ' . $db->quote($obj->value)
 				 ->set($db->quoteName('update') . ' = ' . $db->quote($update))
 				 ->set($db->quoteName('update_time') . ' = ' . $db->quote($update_time))

 				 //->where('dio_id > 0')
 		      ->where($db->quoteName('index') . ' = ' . $db->quote($obj->index))
 				  ->where($db->quoteName('dio_id') . ' = ' . $db->quote($obj->ipaddr));

           //echo($query);

 				  $db->setQuery($query)->execute();
 		 }

				public static function getDIOAlarm($id,$status)
				{

					  // Create a new query object.
					  $db = JFactory::getDbo();
					  $query = $db->getQuery(true);

					  // Select the required fields from the table.
				    $query
				    ->select('DISTINCT a.*');

				    $query->from('`#__dio_alarm_table` AS a');

				    $query->where('a.state = 1');

						$query->where('a.group = '.$id);

            if($status == 1)
						{
							   $query->where('a.timeout = 0');

						}

		        $db->setQuery($query);

		        //echo($query);
				    $items = (array) $db->loadObjectList();

				    return $items;
				}
				public static function file_upload($file,$obj)
			  {
					// Clean up filename to get rid of strange characters like spaces etc
					$filename = JFile::makeSafe($file['name']);
					JLog::add("123 ".$filename, JLog::INFO, 'jerror');

			    if(empty($filename))    return;

			    $ext =  JFile::getExt($filename);
					if(empty($ext))
					    $ext = $filename;
					JLog::add("456 ".$ext, JLog::INFO, 'jerror');

					$ext = strtolower($ext);

					$time = date ("YmdHis");

			    $myfilename = $time.'-'.$obj->myid .".". $ext;

			    $ds = '/';
					// Set up the source and destination of the file
					$src = $file['tmp_name'];
					$path = JPATH_COMPONENT . $ds . "uploads";
			    $dest1 = $path . $ds . $obj->group;

			    $dest = $dest1 . $ds . $myfilename;
					$path1 = 'components/com_floor/uploads';
					if(isset($obj->group))
			        $img1 = $path1 . $ds . $obj->group;
					else
					    $img1 = $path1;

					$img = $img1 . $ds . $myfilename;
/*
          JLog::add($img1, JLog::INFO, 'jerror');
          JLog::add($ds, JLog::INFO, 'jerror');
					JLog::add($img, JLog::INFO, 'jerror');
					*/
					// First check if the file has the right extension, we need jpg only
					if ($ext == 'jpg' || $ext == 'png')
					{

					   // TODO: Add security checks
			       if(JFolder::exists($path) == false)
						 {

							 JFolder::create($path);
						 }

						 if(JFolder::exists($dest1) == false)
						 {

							 JFolder::create($dest1);
						 }

					   if (JFile::upload($src, $dest))
					   {

							 $obj->path = $img;
			         $obj->picture = $myfilename;
			         $obj->dir = $path;

					      // Redirect to a page of your choice
								JLog::add(JText::_('COM_FLOOR_UPLOAD_OK'), JLog::INFO, 'jerror');
			          return true;
					   }
					   else
					   {
					      // Redirect and throw an error message
								JLog::add(JText::_('COM_FLOOR_UPLOAD_ERROR'), JLog::WARNING, 'jerror');

					   }

					}
					else
					{
					   // Redirect and notify user file is not right extension
			       JLog::add(JText::_('COM_FLOOR_EXTENSION_ERROR'), JLog::WARNING, 'jerror');
					}

					return false;

			  }
				public static function is_localip($ip)
			  {
					//echo($ip);
					return true;
					if($ip == "127.0.0.1")
					{
						return true;
					}
          return false;
					//return true;

				}
				public static function getSIPnumber($num)
				{
					$tok = strtok($num, "@");
					$rest = "";
		      if(!empty($tok))
					{
						$rest = substr($tok, 4);
					}

					return $rest;

		    }

				public static function getMenus()
				{
					// Create a new query object.
					$db = JFactory::getDbo();
					$query = $db->getQuery(true);

					$query
						 ->select('DISTINCT a.*');

					$query->from('`#__menu` AS a');

			    $query->where("a.published = 1");
					$query->where("a.menutype LIKE 'mainmenu'");
					$query->where("a.link LIKE '%filter_building%'");

					 //JLog::add($query, JLog::INFO, 'jerror');
					 $db->setQuery($query);
			     //echo($query);
					 //JLog::add($query, JLog::INFO, 'jerror');

					 $items = (array) $db->loadObjectList();

					 return $items;
				}

				public static function getRelayList()
				{
					// Create a new query object.
					$db = JFactory::getDbo();
					$query = $db->getQuery(true);

					$query
						 ->select('DISTINCT a.*');

					$query->from('`#__urllist_table` AS a');

					$query->where('a.state = 1');
					$query->where('a.type = 2');

					 //JLog::add($query, JLog::INFO, 'jerror');
					 $db->setQuery($query);
					 //echo($query);
					 //JLog::add($query, JLog::INFO, 'jerror');

					 $items = (array) $db->loadObjectList();

					 return $items;
				}
				public static function getUrlList()
				{
					// Create a new query object.
					$db = JFactory::getDbo();
					$query = $db->getQuery(true);

					$query
						 ->select('DISTINCT a.*');

					$query->from('`#__urllist_table` AS a');

					$query->where('a.state = 1');

					 //JLog::add($query, JLog::INFO, 'jerror');
					 $db->setQuery($query);
			     //echo($query);
					 //JLog::add($query, JLog::INFO, 'jerror');

					 $items = (array) $db->loadObjectList();

					 return $items;
				}
				public static function send_alarm($obj, $echo = true)
				{

					$items = TopHelpersUtility::getBaList();

					foreach($items as $i=>$item)
					{
						$url = $item->url.'/index.php?option="com_floor&task=sroots.updateAlarm';
               			$send1 = 'curl http://'.$url.'&alarm='.$obj->number.$obj->msg.'&status='.$obj->status.'" --connect-timeout 3 -o /tmp/updateAlarm'.$obj->status.$item->url. ' &';
						system($send1);
						if ($echo)
						{
							echo($send1);
						}
					}

					self::phoneAlarm2($obj);
				}

		public static function get24DIORoots()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.enable,a.id,a.name,a.dio_port,a.vendor');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$ddiopage );

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getPhoneDirs()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name,a.online,a.offline');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$page );
			$query->where('a.type = 2 OR a.type = 3');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}
		public static function getIPDirs()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name,a.online,a.offline');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$page );
			$query->where('a.type = 2 OR a.type = 3');

			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}
		public static function getDoorCardDirs()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name,a.enable');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$doorpage );


			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}



		public static function getConditionDirs()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$cdpage );


			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getAlarmRoots()
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$adpage );


			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function get24DIODevices($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.enable,a.id,a.name,a.addr,a.port,a.device_id,a.dio_type');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$dpage );
      $query->where('a.parent = '.$id);

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getSubConditions($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$apage);

		  $query->where('a.alarm = ' . $id);

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getConditionNodes($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$cpage);

		  $query->where('a.id = ' . $id);

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}
		public static function getDeviceBrandById($id)
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->select('brand.id, brand.name, brand.web_view_subpath, brand.web_view_port, brand.web_view_scheme');
			$query->from('`#__device_brand` AS brand');
			$query->where('brand.enable = 1');
			$query->where('brand.id ='.$id);
			$db->setQuery($query);
			return $db->loadResult();

		}
		public static function getDeviceBrands()
		{
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);
			$query->select('brand.id, brand.name,  brand.web_view_subpath, brand.web_view_port, brand.web_view_scheme');
			$query->from('`#__device_brand` AS brand');
			$query->where('brand.enable = 1');
			$db->setQuery($query);
			return (array) $db->loadObjectList();
			// return (array);
		}
		public static function getSubDoorCards($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name,a.enable,a.path');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.parent = '.$id );


			 //JLog::add($query, JLog::INFO, 'jerror');
			 $db->setQuery($query);
			 //echo($query);
			 //JLog::add($query, JLog::INFO, 'jerror');

			 $items = (array) $db->loadObjectList();

			 return $items;
		}

		public static function getSubRS485s($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.addr,a.dio_type,a.enable');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');

      $query->where('a.group = ' . $id);

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getSubAlarms($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.name,a.iscaller,a.caller,a.callee,a.timeout_alarm_c,a.isvideo,a.path,a.isvideo1,a.path1,a.isvideo2,a.path2,a.isvideo3,a.path3,a.cctv_timeout,a.msg_number,a.msg_name,a.msg_type,a.msg_context,a.msg_voice_path,a.msg_enable,a.alert_enable,a.alert_context');

			$query->from('`#__top_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.note1 = '.self::$apage );
      $query->where('a.alarm = ' . $id);

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getDIONodes($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.*');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.dio_id = '.$id );
      $query->order('a.index');

			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}
		public static function getDoorCardNodes($id)
		{
			// Create a new query object.
			$db = JFactory::getDbo();
			$query = $db->getQuery(true);

			$query
				 ->select('DISTINCT a.id,a.index,a.dio_type,a.note,a.info,a.path');

			$query->from('`#__device_table` AS a');

			$query->where('a.state = 1');
			$query->where('a.dio_id = '.$id );


			 $db->setQuery($query);

			 $items = (array) $db->loadObjectList();

			 return $items;

		}

		public static function getAlarmNodes($group_id)
	  {
		    $db = JFactory::getDbo();
		    $query = $db->getQuery(true);

		    // Select the required fields from the table.
	     $query
	     ->select('DISTINCT a.id,a.is_video,a.parent,a.device,a.index,a.timeout,a.value,a.value2, a.return_last_state, a.cascade_cancel');

	     $query->from('`#__dio_alarm_table` AS a');

	     $query->where('a.state = 1');

			 $query->where('a.group = '. $group_id);

	     $db->setQuery($query);

	     $items = (array) $db->loadObjectList();

	     return $items;
	  }


    public static function get24DIODevice()
    {
        $items = self::get24DIORoots();
		$timing_sets = self::getTimingSets();

        foreach($items as $i=>$item)
				{
					$devices = self::get24DIODevices($item->id);
					$item->devices = $devices;

					foreach($devices as $i1=>$item1)
					{
						$nodes = self::getDIONodes($item1->id);
						foreach($nodes as $i2=>$node)
						{
							$node->timing_sets = array();
							foreach ($timing_sets as $i3 => $timing_set) {

								if (in_array($node->id, $timing_set->supressed_alarm_nodes))
								{
									$t = new stdClass();
									$t->start_hour = $timing_set->start_hour;
									$t->start_minute = $timing_set->start_minute;
									$t->end_hour = $timing_set->end_hour;
									$t->end_minute = $timing_set->end_minute;
									$t->weekdays = $timing_set->weekdays;
									array_push($node->timing_sets, $t);
								}
							}
						}
						$item1->nodes = $nodes;
					}
				}

				return $items;
    }

		public static function getAlarms()
    {
        $items = self::getAlarmRoots();

        foreach($items as $i=>$item)
				{
					$subalarms = self::getSubAlarms($item->id);
					$item->subalarms = $subalarms;

					foreach($subalarms as $i1=>$item1)
					{
						$nodes = self::getAlarmNodes($item1->id);
						$item1->nodes = $nodes;
					}

				}

				return $items;
    }
		public static function getPhones()
    {
        $items = self::getSIPInfo();

        foreach($items as $i=>$item)
				{
					$item->Phones = self::getPhoneDevicesByGroup($item->id);

				}

				return $items;
    }
		public static function getIPs()
    {
        $items = self::getIPDirs();

        foreach($items as $i=>$item)
				{
					$item->IPs = self::getIPDevices($item->id,0,true);

				}

				return $items;
    }

		public static function getDoorCards()
    {
        $items = self::getDoorCardDirs();

				foreach($items as $i=>$item)
				{
					$devices = self::getSubDoorCards($item->id);
					$item->devices = $devices;

					foreach($devices as $i1=>$item1)
					{
						$nodes = self::getDoorCardNodes($item1->id);
						$item1->nodes = $nodes;
					}

				}

				return $items;
    }

		public static function getConditions()
    {
        $items = self::getConditionDirs();

        foreach($items as $i=>$item)
				{
					$subconditions = self::getSubConditions($item->id);
					$item->subconditions = $subconditions;

					foreach($subconditions as $i1=>$item1)
					{
						$nodes = self::getConditionNodes($item1->id);
						$item1->nodes = $nodes;
					}

				}

				return $items;
    }

	public static function update_alarm_kw($alarm_kw)
    {

    	$myaccounts = TopHelpersUtility::getMyAccount();

		if(count($myaccounts))
		{
			$myaccounts[0]->elec_alarm = $alarm_kw;
          	self::update_acc_alarm_kw($myaccounts[0]);
		  	TopHelpersUtility::send_reload_center();
		}
	}

		protected function update_acc_alarm_kw($obj)
		{
			  self::update_acc($obj);

		}

}

