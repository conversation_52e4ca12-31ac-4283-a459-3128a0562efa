#include <ctime>
#include "elecNode.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNode::elecNode()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
  temperature1 = 0;
  temperature2 = 0;
  temperature3 = 0;
  temperature4 = 0;
  version = 0;
  node_type = 0;
}

elecNode::~elecNode()
{

}

int elecNode::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it,&m_id,"id");

  set_int_value(it,&m_dio_type,"dio_type");
  // float elec_float = 0;
  // set_float_value(it,&elec_float,"elec_kwh");
  // elec_float *= 100;
  // kwh = elec_float;
  // old_kwh = kwh;

  int reg_arr[]=
  {

    2699,// KWH
    3059,// KW total
    3027,// V A-N
    3009,// A avg
    3109,// HZ
    3191,//F
    3029,// V BN
    3031,// V CN
    2999,//A A
    3001,//A B
    3003,//A C
    3067,//KVAR
  };

  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for(int i=0;i<sizeof(reg_arr)/sizeof(reg_arr[0]);i++)
  {
      send[0] = m_addr;
      send[1] = 0x03;
      send[2] = reg_arr[i]/256;
      send[3] = reg_arr[i]%256;
      send[4] = 0;
      send[5] = 0x02;

      crc = crc_chk(send,6);
      send[6]=crc%256;
      send[7]=crc/256;

      RS485Msg msg;
      msg.setData(send,8);
      m_msg.push_back(msg);

  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

void elecNode::setKWH(long kwh)
{
    this->kwh = kwh;
}
void elecNode::setElecNode(long kwh, int v1, int v2, int v3, int i1, int i2, int i3, int freq, int pf, int kvar, int kw)
{
  this->kwh = kwh;
  this->volage = v1;
  this->V_BN = v2;
  this->V_CN = v3;
  this->A_A = i1;
  this->A_B = i2;
  this->A_C = i3;
  this->freq = freq;
  this->power_factor = pf;
  this->kvar = kvar;
  this->kwh = kwh;
  this->kw = kw;
}
long elecNode::getKWH()
{
    return kwh;
}

bool elecNode::set_data(uint8_t *p_data,int len)
{
    if(p_data[0] != m_addr)
    {
      return false;
    }
    int aspect_length = 7;
    uint16_t crc = crc_chk(p_data, aspect_length);
    if (p_data[1] == 0x83 || p_data[1] != 0x03)
    {
      for (size_t i = 0; i < len; i++)
      {
        printf("%02X ",p_data[i]);
        /* code */
      }

      cout << "elec response 83 error" << endl;
      m_index = 0;
      return true;
    }
    if (crc%256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length+1])
    {
      for (size_t i = 0; i < len; i++)
      {
        printf("%02X ",p_data[i]);
        /* code */
      }

      cout << "elec crc checksum error " << aspect_length << ", m_index:"  << m_index << endl;
      m_index = 0;
      return true;
    }
    //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
    //usleep(10000);
    float val;
    float * p_val;
    int int_val;
    uint8_t buf[4];

    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    p_val = (float *)&buf[0];

    //p_val = (float *)&buf[0];

    val = *p_val;

    int_val = val*100;
    //cout<<int_val<<endl;
    //fflush(stdout);

    switch(m_index)
    {
      case 0:
      kwh = int_val;
      break;
      case 1:
      kw = int_val;
      break;
      case 2:
      volage = int_val;
      break;
      case 3:
      current = int_val;
      break;
      case 4:
      freq = int_val;
      break;
      case 5:
      power_factor = int_val;

      break;
      case 6:
      V_BN = int_val;
      break;
      case 7:
      V_CN = int_val;
      break;
      case 8:
      A_A = int_val;
      break;
      case 9:
      A_B = int_val;
      break;
      case 10:
      A_C = int_val;

      break;
      case 11:
      kvar = int_val;
      updateMessage();
      break;

    }

    m_index++;

    if(m_index >= m_msg.size())
    {
      m_index = 0;
    }

   return true;
}
int elecNode::getIndex()
{
  return index;
}
int elecNode::getPid()
{
  return m_pid;
}
bool elecNode::updateMessageFromBa()
{
  return updateMessage();
}
bool elecNode::updateMessage()
{
  bool is_show;

  is_show = false;

  int t = static_cast<int> (time(NULL));

  long new_kwh = kwh;

  string update_kwh = "update_elec2";
  string log = "/tmp/sendrs485elecmsg";
  string LOG = "/tmp/SENDRS485ELECSG";
  if (t > update_timestamp +  1* 60)
  {
    update_timestamp = t;
    if(t > (update_kwh_timestamp+HELF_HOUR))
    {
      update_kwh_timestamp = t;
      // update_kwh = "update_kwh";

      if(old_kwh == 0)
      {
        old_kwh = kwh;
      }
      else if(kwh < old_kwh)
      {
        old_kwh = kwh;
      }

      new_kwh = kwh - old_kwh;
      old_kwh = kwh;

      is_show = true;
      log = "/tmp/sendrs485elecmsg1";
      LOG = "/tmp/SENDRS485ELECSG1";

      baUtil::saveElecKWH();

    }

    int elec_kw = baUtil::calcMainTotal(m_id,kw/100.0)*100;


    std::stringstream ss;

    //if(m_print_cnt == 0)
    if (m_dio_type == GENERAL_SOLAR_DEVICE)
    {
       ss << "/index.php?option=\"com_floor&task=sroots."<<update_kwh<<"&kw="<< kw <<"&elec_kw="<< kw<<"&kwh="<<kwh<<"&id="<<m_id<<"\" ";
    }
    if (m_dio_type == TATUNG_ELEC_DEVICE)
    {
      // cout << "tatung_new_kwh:" << new_kwh << ", kwh=" << kwh << ", old_kwh= " << old_kwh  << endl;
      // cout << ", update_kwh_timestamp=" << update_kwh_timestamp << ", update_timestamp=" << update_timestamp << ", t=" << t << endl;;
      ss << "/index.php?option=\"com_floor&task=sroots."<<update_kwh<<"&kwh="<<kwh<<"&id="<<m_id<<"\" ";
      //&m_index=" << m_index << "&index=" << index << "&m_addr="<< m_addr <<"\" ";
    }
    else if (m_dio_type == WEEMA_ELEC_DEVICE_1P || m_dio_type == WEEMA_ELEC_DEVICE_3P)
    {
      // cout << "weema power: " << temperature1 << temperature2 <<temperature3 << temperature4 <<endl;
      ss << "/index.php?option=\"com_floor&task=sroots."<<update_kwh<<"&kwh="<<kwh<<"&kw="<<kw<<"&volage="<<volage<<"&current="<<current<<"&freq="<<freq<<"&power_factor="<<power_factor<<"&id="<<m_id<<"&note="<<kwh;
      ss << "&version=" << version << "&node_type=" << node_type;
      ss <<"&V_BN="<<V_BN<<"&V_CN="<<V_CN<<"&A_A="<<A_A<<"&A_B="<<A_B<<"&A_C="<<A_C<<"&KVAR="<<kvar<<"&temperature1="<< temperature1<<"&temperature2="<<temperature2<<"&temperature3="<<temperature3<<"&temperature4="<<temperature4 << "&elec_kw="<<elec_kw<<"\" ";

      // cout << ss.str().c_str() << endl;
    }
    else if (m_dio_type == KT_MK3_ELEC_DEVICE)
    {
      // KT-MK3 電表資料傳送格式
      ss << "/index.php?option=\"com_floor&task=sroots."<<update_kwh<<"&kwh="<<kwh<<"&kw="<<kw<<"&volage="<<volage<<"&current="<<current<<"&freq="<<freq<<"&power_factor="<<power_factor<<"&id="<<m_id<<"&note="<<kwh;
      ss <<"&V_BN="<<V_BN<<"&V_CN="<<V_CN<<"&A_A="<<A_A<<"&A_B="<<A_B<<"&A_C="<<A_C<<"&KVAR="<<kvar<<"&elec_kw="<<elec_kw<<"\" ";
    }
    else
    {
      ss << "/index.php?option=\"com_floor&task=sroots."<<update_kwh<<"&kwh="<<kwh<<"&kw="<<kw<<"&volage="<<volage<<"&current="<<current<<"&freq="<<freq<<"&power_factor="<<power_factor<<"&id="<<m_id<<"&note="<<kwh;
      ss <<"&V_BN="<<V_BN<<"&V_CN="<<V_CN<<"&A_A="<<A_A<<"&A_B="<<A_B<<"&A_C="<<A_C<<"&KVAR="<<kvar<<"&elec_kw="<<elec_kw<<"\" ";
    }
    WSendMsg msg;

    //is_show = true;
    msg.set(ss.str(),log,LOG,false,false);
    msg.set_local(false);
    baUtil::add_sendMessage(&msg);

  }

  return true;

}
