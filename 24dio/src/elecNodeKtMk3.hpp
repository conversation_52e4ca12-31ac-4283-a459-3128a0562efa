#ifndef __ELECNODEKTMK3_HPP__
#define __ELECNODEKTMK3_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"

/**
 * KT-MK3 電表節點類別
 * 支援 Modbus RTU OverTCP，Input Register (32bit sw. float)
 * 
 * 測量項目：
 * 1. Vab (7~8)     - 線電壓 AB
 * 2. Vbc (9~10)    - 線電壓 BC  
 * 3. Vca (11~12)   - 線電壓 CA
 * 4. Ia (13~14)    - A相電流
 * 5. Ib (15~16)    - B相電流
 * 6. Ic (17~18)    - C相電流
 * 7. I (67~68)     - 總電流
 * 8. <PERSON><PERSON> (41~42)  - 無功功率 (*0.001)
 * 9. pf (49~50)    - 功率因數
 * 10. freq (51~52) - 頻率
 * 11. kwh (79~80)  - 累積電能 (*0.001)
 * 12. kw (25~26)   - 有功功率 (*0.001)
 */
class elecNodeKtMk3 : public elecNode
{
public:
    elecNodeKtMk3();
    virtual ~elecNodeKtMk3();

    /**
     * 設定節點參數
     * @param value JSON 設定值
     * @return 設定結果
     */
    int set(Json::Value value);
    
    /**
     * 處理接收到的資料
     * @param p_data 資料指標
     * @param len 資料長度
     * @return 處理結果
     */
    bool set_data(uint8_t *p_data, int len);
    
    /**
     * 設定累積電能值
     * @param kwh 累積電能值
     */
    void setKWH(long kwh);
    
    /**
     * 取得累積電能值
     * @return 累積電能值
     */
    long getKWH();

private:
    /**
     * 將 32bit float 資料從 Modbus 格式轉換為浮點數
     * @param p_data 資料指標
     * @param offset 資料偏移量
     * @return 轉換後的浮點數值
     */
    float convertModbusFloatToFloat(uint8_t *p_data, int offset);
};

#endif
