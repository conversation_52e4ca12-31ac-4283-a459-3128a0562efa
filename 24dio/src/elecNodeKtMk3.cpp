#include "elecNodeKtMk3.hpp"
#include "utility.hpp"
#include "baUtil.hpp"

elecNodeKtMk3::elecNodeKtMk3()
{
}

elecNodeKtMk3::~elecNodeKtMk3()
{
}

int elecNodeKtMk3::set(Json::Value it)
{
    // 設定基本參數
    analogNode::set(it);
    set_int_value(it, &m_id, "id");
    set_int_value(it, &m_dio_type, "dio_type");

    // KT-MK3 電表的暫存器位址陣列
    // 每個位址對應一個 32bit float 值（佔用 2 個暫存器）
    int reg_arr[] = {
        7,   // Vab (7~8)
        9,   // Vbc (9~10)
        11,  // Vca (11~12)
        13,  // Ia (13~14)
        15,  // Ib (15~16)
        17,  // Ic (17~18)
        67,  // I (67~68) - 總電流
        41,  // <PERSON><PERSON> (41~42) - 無功功率 (*0.001)
        49,  // pf (49~50) - 功率因數
        51,  // freq (51~52) - 頻率
        79,  // kwh (79~80) - 累積電能 (*0.001)
        25   // kw (25~26) - 有功功率 (*0.001)
    };

    uint8_t send[MAX_RS485_DATA];
    uint16_t crc;

    // 為每個暫存器建立 Modbus 讀取指令
    for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
    {
        send[0] = m_addr;           // 設備位址
        send[1] = 0x04;             // 功能碼：讀取 Input Register
        send[2] = reg_arr[i] / 256; // 暫存器位址高位元組
        send[3] = reg_arr[i] % 256; // 暫存器位址低位元組
        send[4] = 0;                // 讀取數量高位元組
        send[5] = 0x02;             // 讀取數量低位元組（2個暫存器 = 32bit float）

        // 計算並加入 CRC 校驗碼
        crc = crc_chk(send, 6);
        send[6] = crc % 256;        // CRC 低位元組
        send[7] = crc / 256;        // CRC 高位元組

        // 建立訊息並加入佇列
        RS485Msg msg;
        msg.setData(send, 8);
        m_msg.push_back(msg);
    }

    // 初始化迭代器
    iterator = m_msg.begin();

    // 將此節點加入電表節點管理
    baUtil::addElecNodes(this);

    return 1;
}

float elecNodeKtMk3::convertModbusFloatToFloat(uint8_t *p_data, int offset)
{
    // KT-MK3 使用 32bit sw. float 格式
    // 需要將 4 個位元組重新排列為正確的 float 格式
    uint8_t buf[4];
    
    // Modbus 32bit float 的位元組順序轉換
    buf[0] = p_data[offset + 1];     // 低位暫存器的低位元組
    buf[1] = p_data[offset];         // 低位暫存器的高位元組
    buf[2] = p_data[offset + 3];     // 高位暫存器的低位元組
    buf[3] = p_data[offset + 2];     // 高位暫存器的高位元組
    
    return *(float *)&buf[0];
}

bool elecNodeKtMk3::set_data(uint8_t *p_data, int len)
{
    // 檢查設備位址是否正確
    if (p_data[0] != m_addr)
    {
        return false;
    }

    // 從接收到的資料中提取 32bit float 值
    float float_value = convertModbusFloatToFloat(p_data, 3);

    // 根據目前的訊息索引處理不同的測量值
    switch (m_index)
    {
    case 0: // Vab (7~8)
        volage = (int)(float_value * 100);  // 轉換為整數（*100）
        break;
        
    case 1: // Vbc (9~10)
        V_BN = (int)(float_value * 100);
        break;
        
    case 2: // Vca (11~12)
        V_CN = (int)(float_value * 100);
        break;
        
    case 3: // Ia (13~14)
        current = A_A = (int)(float_value * 1000);  // 電流轉換為 mA
        break;
        
    case 4: // Ib (15~16)
        A_B = (int)(float_value * 1000);
        break;
        
    case 5: // Ic (17~18)
        A_C = (int)(float_value * 1000);
        break;
        
    case 6: // I (67~68) - 總電流
        // 總電流可以用於驗證或其他用途
        break;
        
    case 7: // Kvar (41~42) - 無功功率 (*0.001)
        kvar = (int)(float_value * 0.001 * 1000);  // 套用縮放係數並轉換
        break;
        
    case 8: // pf (49~50) - 功率因數
        power_factor = (int)(float_value * 1000);
        break;
        
    case 9: // freq (51~52) - 頻率
        freq = (int)(float_value * 100);
        break;
        
    case 10: // kwh (79~80) - 累積電能 (*0.001)
        kwh = (long)(float_value * 0.001 * 100);  // 套用縮放係數並轉換
        updateMessage();  // 更新訊息
        break;
        
    case 11: // kw (25~26) - 有功功率 (*0.001)
        kw = (int)(float_value * 0.001 * 100);  // 套用縮放係數並轉換
        break;
    }

    // 移動到下一個訊息
    m_index++;

    // 如果已處理完所有訊息，重置索引
    if (m_index >= m_msg.size())
    {
        m_index = 0;
    }

    return true;
}

void elecNodeKtMk3::setKWH(long kwh)
{
    this->kwh = kwh;
}

long elecNodeKtMk3::getKWH()
{
    return kwh;
}
